/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ["localhost", "cromitopia.com", "api.cromitopia.com", "ecom.test"],
    formats: ["image/webp", "image/avif"],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 60,
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  reactStrictMode: false,
  swcMinify: true,
  compress: true,
  poweredByHeader: false,
  generateEtags: true,

  // Performance optimizations
  experimental: {
    optimizePackageImports: ["lucide-react", "react-icons"],
  },

  // Redirects for SEO
  async redirects() {
    return [
      {
        source: "/home",
        destination: "/",
        permanent: true,
      },
    ];
  },

  // Rewrites for API proxy (development only)
  async rewrites() {
    if (process.env.NODE_ENV === "development") {
      return [
        {
          source: "/api/proxy/:path*",
          destination: `${process.env.NEXT_PUBLIC_API_URL}/:path*`,
        },
      ];
    }
    return [];
  },
};

export default nextConfig;
