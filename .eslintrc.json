{"extends": ["eslint:recommended", "plugin:react/recommended", "plugin:import/errors", "plugin:import/warnings", "plugin:prettier/recommended"], "plugins": ["import", "react", "prettier"], "rules": {"prettier/prettier": "error", "import/order": ["error", {"groups": [["builtin", "external"], ["internal", "parent", "sibling", "index"]], "newlines-between": "always", "alphabetize": {"order": "asc", "caseInsensitive": true}}]}, "settings": {"react": {"version": "detect"}}}