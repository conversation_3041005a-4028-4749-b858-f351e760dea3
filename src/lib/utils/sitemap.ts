import { MetadataRoute } from 'next'

// Utility function to create sitemap entries
export function createSitemapEntry(
  url: string,
  lastModified?: Date | string,
  changeFrequency?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never',
  priority?: number
): MetadataRoute.Sitemap[0] {
  return {
    url,
    lastModified: lastModified ? new Date(lastModified) : new Date(),
    changeFrequency: changeFrequency || 'weekly',
    priority: priority || 0.5,
  }
}

// Static routes configuration
export const staticRoutes = [
  { path: '', priority: 1, changeFrequency: 'daily' as const },
  { path: '/shop', priority: 0.9, changeFrequency: 'daily' as const },
  { path: '/cart', priority: 0.8, changeFrequency: 'weekly' as const },
  { path: '/contact-us', priority: 0.7, changeFrequency: 'monthly' as const },
  { path: '/our-story', priority: 0.6, changeFrequency: 'monthly' as const },
  { path: '/reviews', priority: 0.6, changeFrequency: 'weekly' as const },
  { path: '/policies', priority: 0.3, changeFrequency: 'yearly' as const },
  { path: '/login', priority: 0.5, changeFrequency: 'monthly' as const },
  { path: '/register', priority: 0.5, changeFrequency: 'monthly' as const },
]

// Function to generate static routes for sitemap
export function generateStaticRoutes(baseUrl: string): MetadataRoute.Sitemap {
  return staticRoutes.map(route => 
    createSitemapEntry(
      `${baseUrl}${route.path}`,
      new Date(),
      route.changeFrequency,
      route.priority
    )
  )
}

// Function to validate and sanitize URLs
export function sanitizeUrl(baseUrl: string, path: string): string {
  // Remove any double slashes and ensure proper URL format
  const cleanPath = path.startsWith('/') ? path : `/${path}`
  return `${baseUrl.replace(/\/$/, '')}${cleanPath}`
}

// Function to batch process large datasets for sitemap
export function batchProcessSitemapEntries<T>(
  items: T[],
  processor: (item: T) => MetadataRoute.Sitemap[0],
  batchSize: number = 1000
): MetadataRoute.Sitemap {
  const result: MetadataRoute.Sitemap = []
  
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize)
    const processedBatch = batch.map(processor)
    result.push(...processedBatch)
  }
  
  return result
}
