/**
 * SEO Utilities for enhanced meta tags, Open Graph, and structured data
 */

const SITE_CONFIG = {
  name: "CromiTopia",
  description:
    "Discover adorable keychains, kawaii stationery, and cute accessories at CromiTopia! Shop the best Japanese kawaii products with worldwide shipping.",
  url: process.env.NEXT_PUBLIC_BASE_URL || "https://cromitopia.com",
  logo: "/images/logo.png",
  favicon: "/favicon.ico",
  themeColor: "#000000",
  locale: "en_US",
  type: "website",
  twitter: "@cromitopia",
  facebook: "cromitopia",
  instagram: "cromitopia",
};

/**
 * Generate comprehensive meta tags for pages
 */
export function generateMetaTags({
  title,
  description,
  keywords = [],
  image,
  url,
  type = "website",
  noIndex = false,
  canonical,
  alternates = {},
}) {
  const fullTitle = title ? `${title} | ${SITE_CONFIG.name}` : SITE_CONFIG.name;
  const fullDescription = description || SITE_CONFIG.description;
  const fullUrl = url ? `${SITE_CONFIG.url}${url}` : SITE_CONFIG.url;
  const fullImage = image
    ? `${SITE_CONFIG.url}${image}`
    : `${SITE_CONFIG.url}${SITE_CONFIG.logo}`;

  const metadata = {
    title: fullTitle,
    description: fullDescription,
    keywords: keywords.length > 0 ? keywords.join(", ") : undefined,
    robots: noIndex ? "noindex,nofollow" : "index,follow",
    canonical: canonical || fullUrl,

    // Open Graph
    openGraph: {
      title: fullTitle,
      description: fullDescription,
      url: fullUrl,
      siteName: SITE_CONFIG.name,
      images: [
        {
          url: fullImage,
          width: 1200,
          height: 630,
          alt: fullTitle,
        },
      ],
      locale: SITE_CONFIG.locale,
      type: type,
    },

    // Twitter Card
    twitter: {
      card: "summary_large_image",
      title: fullTitle,
      description: fullDescription,
      images: [fullImage],
      creator: SITE_CONFIG.twitter,
      site: SITE_CONFIG.twitter,
    },

    // Additional meta tags
    other: {
      "theme-color": SITE_CONFIG.themeColor,
      "msapplication-TileColor": SITE_CONFIG.themeColor,
      "apple-mobile-web-app-capable": "yes",
      "apple-mobile-web-app-status-bar-style": "default",
      "format-detection": "telephone=no",
    },

    // Alternates for internationalization
    alternates: {
      canonical: canonical || fullUrl,
      ...alternates,
    },
  };

  return metadata;
}

/**
 * Generate JSON-LD structured data for organization
 */
export function generateOrganizationSchema() {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: SITE_CONFIG.name,
    url: SITE_CONFIG.url,
    logo: `${SITE_CONFIG.url}${SITE_CONFIG.logo}`,
    description: SITE_CONFIG.description,
    sameAs: [
      `https://twitter.com/${SITE_CONFIG.twitter.replace("@", "")}`,
      `https://facebook.com/${SITE_CONFIG.facebook}`,
      `https://instagram.com/${SITE_CONFIG.instagram}`,
    ],
    contactPoint: {
      "@type": "ContactPoint",
      contactType: "Customer Service",
      availableLanguage: ["English"],
    },
  };
}

/**
 * Generate JSON-LD structured data for products
 */
export function generateProductSchema(product) {
  if (!product) return null;

  const schema = {
    "@context": "https://schema.org",
    "@type": "Product",
    name: product.name,
    description: product.description,
    image: product.images?.map((img) => `${SITE_CONFIG.url}${img}`) || [],
    sku: product.sku,
    brand: {
      "@type": "Brand",
      name: SITE_CONFIG.name,
    },
    offers: {
      "@type": "Offer",
      price: product.price,
      priceCurrency: "INR",
      availability:
        product.stock > 0
          ? "https://schema.org/InStock"
          : "https://schema.org/OutOfStock",
      url: `${SITE_CONFIG.url}/shop/product/${product.slug}`,
      seller: {
        "@type": "Organization",
        name: SITE_CONFIG.name,
      },
    },
  };

  // Add aggregateRating if reviews exist
  if (product.reviews && product.reviews.length > 0) {
    const totalRating = product.reviews.reduce(
      (sum, review) => sum + review.rating,
      0
    );
    const averageRating = totalRating / product.reviews.length;

    schema.aggregateRating = {
      "@type": "AggregateRating",
      ratingValue: averageRating.toFixed(1),
      reviewCount: product.reviews.length,
      bestRating: 5,
      worstRating: 1,
    };
  }

  return schema;
}

/**
 * Generate JSON-LD structured data for breadcrumbs
 */
export function generateBreadcrumbSchema(breadcrumbs) {
  if (!breadcrumbs || breadcrumbs.length === 0) return null;

  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: crumb.name,
      item: `${SITE_CONFIG.url}${crumb.url}`,
    })),
  };
}

/**
 * Generate JSON-LD structured data for website
 */
export function generateWebsiteSchema() {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: SITE_CONFIG.name,
    url: SITE_CONFIG.url,
    description: SITE_CONFIG.description,
    potentialAction: {
      "@type": "SearchAction",
      target: {
        "@type": "EntryPoint",
        urlTemplate: `${SITE_CONFIG.url}/shop?search={search_term_string}`,
      },
      "query-input": "required name=search_term_string",
    },
  };
}

/**
 * Generate JSON-LD structured data for local business (if applicable)
 */
export function generateLocalBusinessSchema(businessInfo) {
  if (!businessInfo) return null;

  return {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    name: SITE_CONFIG.name,
    url: SITE_CONFIG.url,
    description: SITE_CONFIG.description,
    address: {
      "@type": "PostalAddress",
      streetAddress: businessInfo.streetAddress,
      addressLocality: businessInfo.city,
      addressRegion: businessInfo.state,
      postalCode: businessInfo.postalCode,
      addressCountry: businessInfo.country,
    },
    telephone: businessInfo.phone,
    email: businessInfo.email,
    openingHours: businessInfo.openingHours || [],
  };
}

/**
 * Generate canonical URL
 */
export function generateCanonicalUrl(path = "") {
  const cleanPath = path.startsWith("/") ? path : `/${path}`;
  return `${SITE_CONFIG.url}${cleanPath}`;
}

/**
 * Generate hreflang alternates for internationalization
 */
export function generateHreflangAlternates(path = "", locales = []) {
  const alternates = {};

  locales.forEach((locale) => {
    const localePath = locale === "en" ? path : `/${locale}${path}`;
    alternates[locale] = `${SITE_CONFIG.url}${localePath}`;
  });

  return alternates;
}

export { SITE_CONFIG };
