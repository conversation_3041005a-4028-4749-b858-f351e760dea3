import axios from "axios";
import { environment } from "../environment";
import toast from "react-hot-toast";

export const createOrder = async (data) => {
  const token = localStorage.getItem("token");
  if (!token) {
    toast.error("You must be logged in to create an order.");
    return;
  }
  const res = await axios.post(`${environment.URL}/create-order`, data, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res.data;
};
