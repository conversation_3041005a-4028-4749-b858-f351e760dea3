import axios from "axios";

// Use proxy for CORS issues in production if needed
const USE_PROXY = process.env.NEXT_PUBLIC_USE_API_PROXY === "true";
const BASE_URL = USE_PROXY
  ? "/api/proxy"
  : process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000/api/v1";

const apiService = axios.create({
  baseURL: BASE_URL,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
  timeout: 30000, // 30 second timeout for production
  withCredentials: false, // Don't send cookies for CORS
});

// Add request interceptor to include auth token
apiService.interceptors.request.use(
  (config) => {
    // Add auth token if available
    if (typeof window !== "undefined") {
      const token = localStorage.getItem("token");
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle errors
apiService.interceptors.response.use(
  (response) => response, // Pass through successful responses
  (error) => {
    if (error.response && error.response.status === 401) {
      // Handle 401 Unauthorized errors
      console.error("Unauthorized access - logging out...");
      if (typeof window !== "undefined") {
        localStorage.removeItem("token");
        localStorage.removeItem("userData");
        window.location.href = "/login";
      }
    }

    // Log API errors for debugging
    console.error("API Error:", {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      message: error.response?.data?.message || error.message,
      baseURL: error.config?.baseURL,
      fullURL: `${error.config?.baseURL}${error.config?.url}`,
    });

    return Promise.reject(error);
  }
);

export const apiServiceWrapper = {
  async get(endpoint, options = {}) {
    try {
      const response = await apiService.get(endpoint, options);

      return {
        data: response.data?.data || response.data,
        meta: response.data?.meta || {},
        error: false,
        message: response.data?.message || "Success",
      };
    } catch (error) {
      console.error(`❌ API GET Error for ${endpoint}:`, {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          baseURL: error.config?.baseURL,
        },
      });

      return {
        data: null,
        meta: {},
        error: true,
        message:
          error.response?.data?.message || error.message || "An error occurred",
        status: error.response?.status,
        details: {
          endpoint,
          baseURL: BASE_URL,
          fullUrl: `${BASE_URL}${endpoint}`,
          errorType: error.code || "UNKNOWN",
        },
      };
    }
  },

  async post(endpoint, data, options = {}) {
    try {
      const response = await apiService.post(endpoint, data, options);
      return {
        data: response.data?.data || response.data,
        meta: response.data?.meta || {},
        error: false,
        message: response.data?.message || "Success",
      };
    } catch (error) {
      console.error("API POST Error:", error);
      return {
        data: null,
        meta: {},
        error: true,
        message:
          error.response?.data?.message || error.message || "An error occurred",
        status: error.response?.status,
      };
    }
  },

  async put(endpoint, data, options = {}) {
    try {
      const response = await apiService.put(endpoint, data, options);
      return {
        data: response.data?.data || response.data,
        meta: response.data?.meta || {},
        error: false,
        message: response.data?.message || "Success",
      };
    } catch (error) {
      console.error("API PUT Error:", error);
      return {
        data: null,
        meta: {},
        error: true,
        message:
          error.response?.data?.message || error.message || "An error occurred",
        status: error.response?.status,
      };
    }
  },

  async delete(endpoint, options = {}) {
    try {
      const response = await apiService.delete(endpoint, options);
      return {
        data: response.data?.data || response.data,
        meta: response.data?.meta || {},
        error: false,
        message: response.data?.message || "Success",
      };
    } catch (error) {
      console.error("API DELETE Error:", error);
      return {
        data: null,
        meta: {},
        error: true,
        message:
          error.response?.data?.message || error.message || "An error occurred",
        status: error.response?.status,
      };
    }
  },
};

export default apiServiceWrapper;
