import { useState, useEffect } from "react";

const useAuth = () => {
  const [isAuth, setIsAuth] = useState(() => !!localStorage.getItem("token"));

  useEffect(() => {
    const updateAuthStatus = () => {
      const token = localStorage.getItem("token");
      setIsAuth(!!token);
    };

    // Check initially
    updateAuthStatus();

    // Listen for localStorage changes (logout or login in another tab)
    window.addEventListener("storage", updateAuthStatus);

    return () => {
      window.removeEventListener("storage", updateAuthStatus);
    };
  }, []);

  return isAuth;
};

export default useAuth;
