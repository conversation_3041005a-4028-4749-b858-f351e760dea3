import { compareArrays } from "@/lib/utils";

import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import axios from "axios";

// API Endpoints
const API_BASE_URL = `${process.env.NEXT_PUBLIC_API_URL}`;

// Helper to calculate discount-adjusted price
const calculateDiscountedPrice = (price, discount) => {
  if (discount?.percentage) {
    return price - (price * discount.percentage) / 100;
  }
  if (discount?.amount) {
    return price - discount.amount;
  }
  return price;
};

// Helper to calculate total price
const calculateTotals = (items) => {
  const totalPrice = items.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0
  );
  const adjustedTotalPrice = items.reduce(
    (sum, item) =>
      sum + calculateDiscountedPrice(item.price, item.discount) * item.quantity,
    0
  );
  const totalQuantities = items.reduce((sum, item) => sum + item.quantity, 0);

  return { totalPrice, adjustedTotalPrice, totalQuantities };
};

// Initial State
const initialState = {
  cart: null,
  totalPrice: 0,
  adjustedTotalPrice: 0,
  action: null,
  loading: false,
  error: null,
};

export const addToCartAsync = createAsyncThunk(
  "cart/addToCart",
  async (item, { rejectWithValue }) => {
    try {
      const cart_id = localStorage.getItem("cart_id");
      let url = `${API_BASE_URL}/cart`;
      if (cart_id) {
        url = `${API_BASE_URL}/cart/${cart_id}`;
      }
      const response = await axios.post(
        url,
        { ...item },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        }
      );
      localStorage.setItem("cart_id", response.data.id);
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data || "Failed to add item to cart"
      );
    }
  }
);

export const removeCartItemAsync = createAsyncThunk(
  "cart/removeCartItem",
  async (item, { rejectWithValue }) => {
    try {
      const cart_id = localStorage.getItem("cart_id");
      const response = await axios.delete(`${API_BASE_URL}/cart/${cart_id}`, {
        data: { product_id: item.id },
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data || "Failed to remove item from cart"
      );
    }
  }
);

export const clearCartAsync = createAsyncThunk(
  "cart/clearCart",
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}cart/clear`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to clear cart");
    }
  }
);

// Slice Definition
export const cartsSlice = createSlice({
  name: "carts",
  initialState,
  reducers: {
    addToCart: (state, action) => {
      const { id, quantity, ...rest } = action.payload;

      if (!state.cart) {
        state.cart = {
          items: [{ id, quantity, ...rest }],
          totalQuantities: quantity,
        };
      } else {
        const existingItem = state.cart.items.find((item) => item.id === id);

        if (existingItem) {
          existingItem.quantity = quantity;
        } else {
          state.cart.items.push({ id, quantity, ...rest });
        }
      }

      // Recalculate totals
      const totals = calculateTotals(state.cart.items);
      state.totalPrice = totals.totalPrice;
      state.adjustedTotalPrice = totals.adjustedTotalPrice;
      state.cart.totalQuantities = totals.totalQuantities;
    },
    removeCartItem: (state, action) => {
      if (!state.cart) return;

      const { id } = action.payload;
      const itemIndex = state.cart.items.findIndex((item) => item.id == id);

      if (itemIndex >= 0) {
        const item = state.cart.items[itemIndex];
        item.quantity -= 1;

        state.cart.items.splice(itemIndex, 1);

        // Recalculate totals
        const totals = calculateTotals(state.cart.items);
        state.totalPrice = totals.totalPrice;
        state.adjustedTotalPrice = totals.adjustedTotalPrice;
        state.cart.totalQuantities = totals.totalQuantities;
      }
    },

    // Completely remove an item from the cart
    remove: (state, action) => {
      if (!state.cart) return;

      const { id, attributes } = action.payload;
      state.cart.items = state.cart.items.filter(
        (item) =>
          !(item.id === id && compareArrays(item.attributes, attributes))
      );

      // Recalculate totals
      const totals = calculateTotals(state.cart.items);
      state.totalPrice = totals.totalPrice;
      state.adjustedTotalPrice = totals.adjustedTotalPrice;
      state.cart.totalQuantities = totals.totalQuantities;

      // Clear cart if no items remain
      if (state.cart.items.length === 0) {
        state.cart = null;
      }
    },
    // clear cart after order complete
    clearCart: (state) => {
      state.cart = null;
    },
  },
  // extraReducers: (builder) => {
  //   // Remove from Cart
  //   builder.addCase(removeCartItemAsync.pending, (state) => {
  //     state.action = "delete";
  //     state.loading = true;
  //   });
  //   builder.addCase(removeCartItemAsync.fulfilled, (state, action) => {
  //     state.loading = false;
  //     state.cart = action.payload;
  //     const totals = calculateTotals(state.cart?.items || []);
  //     state.totalPrice = totals.totalPrice;
  //     state.adjustedTotalPrice = totals.adjustedTotalPrice;
  //     if (state.cart) {
  //       state.cart.totalQuantities = totals.totalQuantities;
  //     }
  //   });
  //   builder.addCase(removeCartItemAsync.rejected, (state, action) => {
  //     state.loading = false;
  //     state.error = action.payload as string;
  //   });

  //   // Clear Cart
  //   builder.addCase(clearCartAsync.pending, (state) => {
  //     state.loading = true;
  //   });
  //   builder.addCase(clearCartAsync.fulfilled, (state) => {
  //     state.loading = false;
  //     state.cart = null;
  //     state.totalPrice = 0;
  //     state.adjustedTotalPrice = 0;
  //   });
  //   builder.addCase(clearCartAsync.rejected, (state, action) => {
  //     state.loading = false;
  //     state.error = action.payload as string;
  //   });
  // },
});

export const { addToCart, removeCartItem, remove, clearCart } =
  cartsSlice.actions;
export default cartsSlice.reducer;
