import { decryptData } from "@/components/common/sharedMethod";
import apiServiceWrapper from "@/lib/services/apiService";
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
// Define the initial state using that type
const initialState = {
  settings: {},
  subscriberModalSettings: {},
  loading: false,
  error: null,
};

// Define the async thunk to fetch settings
export const fetchSettings = createAsyncThunk(
  "settings/fetchSettings",
  async () => {
    const response = await apiServiceWrapper.get(
      `${process.env.NEXT_PUBLIC_API_URL}/settings`
    );

    return response.data;
  }
);

export const fetchSubscriberModalSettings = createAsyncThunk(
  "settings/fetchSubscriberModalSettings",
  async () => {
    const response = await apiServiceWrapper.get(
      `${process.env.NEXT_PUBLIC_API_URL}/subscriber-settings`
    );
    if (!response.success) {
      throw new Error("Failed to fetch settings");
    }
    const data = decryptData(response.data);
    return JSON.parse(data);
  }
);

export const settingsSlice = createSlice({
  name: "settings",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchSettings.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSubscriberModalSettings.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSubscriberModalSettings.fulfilled, (state, action) => {
        state.subscriberModalSettings = action.payload;
        state.loading = false;
      })
      .addCase(fetchSubscriberModalSettings.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to load settings";
      })
      .addCase(fetchSettings.fulfilled, (state, action) => {
        state.settings = action.payload;
        state.loading = false;
      })
      .addCase(fetchSettings.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to load settings";
      });
  },
});

export default settingsSlice.reducer;
