import apiServiceWrapper from "@/lib/services/apiService";
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";

// Define the initial state using that type
const initialState = {
  addresses: [],
  loading: false,
  error: null,
};

// Define the async thunk to fetch addresses
export const fetchAddresses = createAsyncThunk(
  "address/fetchAddresses",
  async () => {
    const response = await apiServiceWrapper.get(
      `${process.env.NEXT_PUBLIC_API_URL}/addresses`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
      }
    );
    return response.data;
  }
);

// Define the async thunk to post a new address
export const postAddress = createAsyncThunk(
  "address/postAddress",
  async (newAddress) => {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/addresses`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
        body: JSON.stringify(newAddress),
      }
    );

    if (!response.ok) {
      throw new Error("Failed to post new address");
    }
    const addressData = await response.json();
    return addressData.data;
  }
);

// Define the async thunk to update an address
export const updateAddress = createAsyncThunk(
  "address/updateAddress",
  async (updatedAddress) => {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/addresses/${updatedAddress.id}`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
        body: JSON.stringify(updatedAddress),
      }
    );

    if (!response.ok) {
      throw new Error("Failed to update address");
    }
    const addressData = await response.json();
    return addressData.data;
  }
);

// Define the async thunk to delete an address
export const deleteAddress = createAsyncThunk(
  "address/deleteAddress",
  async (addressId) => {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/addresses/${addressId}`,
      {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
      }
    );

    return response.success; // Return the id of the deleted address
  }
);

export const setDefaultAddress = createAsyncThunk(
  "address/setDefaultAddress",
  async (addressId) => {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/addresses/${addressId}/default`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
      }
    );

    return response.success; // Return the id of the set default address
  }
);

export const addressSlice = createSlice({
  name: "address",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Fetch Addresses
      .addCase(fetchAddresses.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAddresses.fulfilled, (state, action) => {
        state.addresses = action.payload;
        state.loading = false;
      })
      .addCase(fetchAddresses.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to load addresses";
      })
      // Post New Address
      .addCase(postAddress.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(postAddress.fulfilled, (state, action) => {
        const newAddress = action.payload;

        // If the new address is set as default, update all other addresses to not be default
        if (newAddress.is_default) {
          state.addresses = state.addresses.map((address) => ({
            ...address,
            is_default: false,
          }));
        }

        state.addresses.push(newAddress); // Add the new address to the state
        state.loading = false;
      })
      .addCase(postAddress.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to post new address";
      })
      // Update Address
      .addCase(updateAddress.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateAddress.fulfilled, (state, action) => {
        const updatedAddress = action.payload;
        const index = state.addresses.findIndex(
          (address) => address.id === updatedAddress.id
        );

        if (index !== -1) {
          // If the updated address is set as default, update all other addresses to not be default
          if (updatedAddress.is_default) {
            state.addresses = state.addresses.map((address) => ({
              ...address,
              is_default: address.id === updatedAddress.id ? true : false,
            }));
          } else {
            // Just update the specific address
            state.addresses[index] = updatedAddress;
          }
        }
        state.loading = false;
      })
      .addCase(updateAddress.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to update address";
      })
      // Delete Address
      .addCase(deleteAddress.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteAddress.fulfilled, (state, action) => {
        const deletedAddressId = action.payload;
        const deletedAddress = state.addresses.find(
          (address) => address.id === deletedAddressId
        );

        // Remove the deleted address
        state.addresses = state.addresses.filter(
          (address) => address.id !== deletedAddressId
        );

        // If the deleted address was the default and there are remaining addresses,
        // set the first remaining address as default
        if (deletedAddress?.is_default && state.addresses.length > 0) {
          state.addresses[0].is_default = true;
        }

        state.loading = false;
      })
      .addCase(deleteAddress.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to delete address";
      })
      // Set Default Address
      .addCase(setDefaultAddress.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(setDefaultAddress.fulfilled, (state, action) => {
        state.addresses = state.addresses.map((address) => {
          if (address.id === action.payload) {
            return { ...address, is_default: true }; // Set the default address
          }
          return { ...address, is_default: false }; // Unset other addresses
        });
        state.loading = false;
      })
      .addCase(setDefaultAddress.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to set default address";
      });
  },
});

export default addressSlice.reducer;
