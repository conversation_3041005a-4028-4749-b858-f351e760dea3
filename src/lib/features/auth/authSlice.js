import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import apiServiceWrapper from "@/lib/services/apiService";

// Thunk for changing password
export const changePassword = createAsyncThunk(
  "auth/changePassword",
  async (data, { rejectWithValue }) => {
    try {
      const response = await apiServiceWrapper.post(
        `${process.env.NEXT_PUBLIC_API_URL}/change-password`,
        data,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        }
      );
      return response;
    } catch ({ response }) {
      return rejectWithValue(response.data.message || "Something went wrong");
    }
  }
);

const authSlice = createSlice({
  name: "auth",
  initialState: {
    user: null,
    loading: false,
    error: null,
    successMessage: null,
  },
  reducers: {
    clearAuthState: (state) => {
      state.error = null;
      state.successMessage = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(changePassword.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.successMessage = null;
      })
      .addCase(changePassword.fulfilled, (state, action) => {
        state.loading = false;
        state.successMessage =
          action.message || "Password changed successfully!";
      })
      .addCase(changePassword.rejected, (state, action) => {
        state.loading = false;
        state.error = action.message || "Failed to change password.";
      });
  },
});

export const { clearAuthState } = authSlice.actions;

export default authSlice.reducer;
