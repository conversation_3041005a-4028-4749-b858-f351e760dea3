import apiServiceWrapper from "@/lib/services/apiService";
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Define the initial state
const initialState = {
  colorSelection: {
    name: "<PERSON>",
    code: "bg-[#4F4631]",
  },
  sizeSelection: "Large",
  recentlyViewedStatus: "idle", // idle, loading, succeeded, failed
  recentlyViewedError: null,
};

// Define the async thunk for the API call
export const storeRecentlyViewedProduct = createAsyncThunk(
  "products/storeRecentlyViewedProduct",
  async (id, { rejectWithValue }) => {
    try {
      if(!id || !localStorage.getItem("token")) return;
      const response = await apiServiceWrapper.post(`${process.env.NEXT_PUBLIC_API_URL}/recently-viewed/${id}`, null, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`, // Replace with your auth token management
        },
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Create the slice
export const productsSlice = createSlice({
  name: "products",
  initialState,
  reducers: {
    setColorSelection: (state, action) => {
      state.colorSelection = action.payload;
    },
    setSizeSelection: (state, action) => {
      state.sizeSelection = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(storeRecentlyViewedProduct.pending, (state) => {
        state.recentlyViewedStatus = "loading";
        state.recentlyViewedError = null;
      })
      .addCase(storeRecentlyViewedProduct.fulfilled, (state) => {
        state.recentlyViewedStatus = "succeeded";
      })
      .addCase(storeRecentlyViewedProduct.rejected, (state, action) => {
        state.recentlyViewedStatus = "failed";
        state.recentlyViewedError = action.payload;
      });
  },
});

// Export actions and reducer
export const { setColorSelection, setSizeSelection } = productsSlice.actions;

export default productsSlice.reducer;
