import { combineReducers, configureStore } from "@reduxjs/toolkit";
import { persistReducer, persistStore } from "redux-persist";
import storage from "@/components/storage";
import productsReducer from "./features/products/productsSlice";
import cartsReducer from "./features/carts/cartsSlice";
import wishlistReducer from "./features/wishlist/wishlistSlice";
import settingsReducer from "./features/settings/settingSlice";
import profileReducer from "./features/profile/profileSlice";
import addressesReducer from "./features/addresses/addressSlice";
import authReducer from "./features/auth/authSlice";

const persistConfig = {
  key: "root",
  storage,
  version: 1,
  whitelist: ["carts", "wishlist", "settings", "addresses"],
};

const rootReducer = combineReducers({
  products: productsReducer,
  carts: cartsReducer,
  wishlist: wishlistReducer,
  settings: settingsReducer,
  profile: profileReducer,
  addresses: addressesReducer,
  auth: authReducer,
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const makeStore = () => {
  const store = configureStore({
    reducer: persistedReducer,
    devTools: process.env.NODE_ENV !== "production",
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }),
  });

  const persistor = persistStore(store);
  return { store, persistor };
};

const store = makeStore().store;

// Infer the type of the store
export { store };
