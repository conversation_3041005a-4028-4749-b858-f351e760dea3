import React, { useState } from "react";

const Tooltip = ({ content, children }) => {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <div
      className="relative inline-block"
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}
      {isVisible && (
        <div className="absolute left-1/2 transform -translate-x-1/2 -top-8 bg-black text-white text-xs font-medium px-2 py-1 rounded shadow-md z-50">
          {content}
        </div>
      )}
    </div>
  );
};

export default Tooltip;
