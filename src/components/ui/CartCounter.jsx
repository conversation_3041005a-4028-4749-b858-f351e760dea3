"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { FaMinus, FaPlus } from "react-icons/fa6";
import { cn } from "@/lib/utils";

const CartCounter = ({ quantity, updateQuantity, className }) => {
  const decrement = () => {
    if (quantity > 1) {
      updateQuantity(quantity - 1);
    }
  };

  const increment = () => {
    updateQuantity(quantity + 1);
  };

  return (
    <div
      className={cn(
        "bg-[#F0F0F0] w-full min-w-[110px] max-w-[110px] sm:max-w-[170px] py-3 md:py-3.5 px-4 sm:px-5 rounded-full flex items-center justify-between",
        className,
      )}
    >
      <Button
        variant="ghost"
        size="icon"
        type="button"
        className="h-5 w-5 sm:h-6 sm:w-6 text-xl hover:bg-transparent"
        onClick={(e) => {
          e.stopPropagation();
          decrement();
        }}
        aria-label="Decrease quantity"
      >
        <FaMinus />
      </Button>
      <span className="font-medium text-sm sm:text-base text-center">
        {quantity}
      </span>
      <Button
        variant="ghost"
        size="icon"
        type="button"
        className="h-5 w-5 sm:h-6 sm:w-6 text-xl hover:bg-transparent"
        onClick={(e) => {
          e.stopPropagation();
          increment();
        }}
        aria-label="Increase quantity"
      >
        <FaPlus />
      </Button>
    </div>
  );
};

export default CartCounter;
