"use client";

import * as React from "react";
import { ChevronRightIcon, DotsHorizontalIcon } from "@radix-ui/react-icons";
import { Slot } from "@radix-ui/react-slot";
import { generateBreadcrumbSchema } from "@/lib/utils/seo";

import { cn } from "@/lib/utils";

const Breadcrumb = React.forwardRef(({ seoData, ...props }, ref) => {
  // Add SEO structured data if provided
  React.useEffect(() => {
    if (seoData && seoData.length > 0) {
      const schema = generateBreadcrumbSchema(seoData);
      if (schema) {
        const script = document.createElement("script");
        script.type = "application/ld+json";
        script.text = JSON.stringify(schema);
        script.id = "breadcrumb-schema";

        // Remove existing breadcrumb schema if present
        const existing = document.getElementById("breadcrumb-schema");
        if (existing) {
          existing.remove();
        }

        document.head.appendChild(script);

        // Cleanup on unmount
        return () => {
          const scriptToRemove = document.getElementById("breadcrumb-schema");
          if (scriptToRemove) {
            scriptToRemove.remove();
          }
        };
      }
    }
  }, [seoData]);

  return (
    <nav
      ref={ref}
      aria-label="breadcrumb"
      itemScope
      itemType="https://schema.org/BreadcrumbList"
      {...props}
    />
  );
});
Breadcrumb.displayName = "Breadcrumb";

const BreadcrumbList = React.forwardRef(({ className, ...props }, ref) => (
  <ol
    ref={ref}
    className={cn(
      "flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",
      className
    )}
    {...props}
  />
));
BreadcrumbList.displayName = "BreadcrumbList";

const BreadcrumbItem = React.forwardRef(
  ({ className, position, ...props }, ref) => (
    <li
      ref={ref}
      className={cn("inline-flex items-center gap-1.5", className)}
      itemProp="itemListElement"
      itemScope
      itemType="https://schema.org/ListItem"
      {...props}
    >
      {props.children}
      {position && <meta itemProp="position" content={position} />}
    </li>
  )
);
BreadcrumbItem.displayName = "BreadcrumbItem";

const BreadcrumbLink = React.forwardRef(
  ({ asChild, className, ...props }, ref) => {
    const Comp = asChild ? Slot : "a";

    return (
      <Comp
        ref={ref}
        className={cn("transition-colors hover:text-foreground", className)}
        itemProp="item"
        {...props}
      />
    );
  }
);
BreadcrumbLink.displayName = "BreadcrumbLink";

const BreadcrumbPage = React.forwardRef(({ className, ...props }, ref) => (
  <span
    ref={ref}
    role="link"
    aria-disabled="true"
    aria-current="page"
    itemProp="name"
    className={cn("font-normal text-foreground", className)}
    {...props}
  />
));
BreadcrumbPage.displayName = "BreadcrumbPage";

const BreadcrumbSeparator = ({ children, className, ...props }) => (
  <li
    role="presentation"
    aria-hidden="true"
    className={cn("[&>svg]:size-3.5", className)}
    {...props}
  >
    {children ?? <ChevronRightIcon />}
  </li>
);
BreadcrumbSeparator.displayName = "BreadcrumbSeparator";

const BreadcrumbEllipsis = ({ className, ...props }) => (
  <span
    role="presentation"
    aria-hidden="true"
    className={cn("flex h-9 w-9 items-center justify-center", className)}
    {...props}
  >
    <DotsHorizontalIcon className="h-4 w-4" />
    <span className="sr-only">More</span>
  </span>
);
BreadcrumbEllipsis.displayName = "BreadcrumbElipssis";

export {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
  BreadcrumbEllipsis,
};
