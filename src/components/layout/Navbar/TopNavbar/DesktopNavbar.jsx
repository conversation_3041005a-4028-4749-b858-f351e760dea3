import React from "react";
import Link from "next/link";
import Image from "next/image";
import {
  NavigationMenu,
  NavigationMenuList,
} from "@/components/ui/navigation-menu";

import { MenuItem } from "./MenuItem";
import { MenuList } from "./MenuList";
import { useSearch } from "@/lib/context/SearchContext";
import useDebounce from "@/lib/hooks/useDebounce";
import CartBtn from "./CartBtn";
import WishList from "./WishList";
import { cn } from "@/lib/utils";
import { integralCF } from "@/styles/fonts";
import { useRouter } from "next/navigation";

const DesktopNavbar = ({ data, isAuthenticated, setIsAuthenticated }) => {
  const router = useRouter();
  const [localSearchTerm, setLocalSearchTerm] = React.useState(" ");
  const { setSearchTerm } = useSearch();

  const debouncedSearchTerm = useDebounce(
    (value) => setSearchTerm(value),
    1000,
  );

  const handleSearchChange = (e) => {
    setLocalSearchTerm(e.target.value);
    debouncedSearchTerm(e.target.value);
  };

  const onSearch = (e) => {
    e.preventDefault();
    router.push("/shop");
  };

  return (
    <div className="flex justify-between items-center">
      <Link
        href="/"
        className={cn([
          integralCF.className,
          "text-2xl lg:text-[32px] flex-shrink-0 mr-4 md:m-0",
        ])}
      >
        <img
          src="/images/logo.png"
          alt="Logo"
          className="h-[35px] md:h-[40px]"
        />
      </Link>
      <NavigationMenu>
        <NavigationMenuList>
          {data.map((item) => (
            <React.Fragment key={item.id}>
              {item.type === "MenuItem" && (
                <MenuItem label={item.label} url={item.url} />
              )}
              {item.type === "MenuList" && (
                <MenuList data={item.children} label={item.label} />
              )}
            </React.Fragment>
          ))}
        </NavigationMenuList>
      </NavigationMenu>
      <form className="bg-white shadow-extra w-[300px] flex rounded-full">
        <input
          type="search"
          placeholder="Search Products..."
          value={localSearchTerm}
          onChange={handleSearchChange}
          className="flex-grow px-4 py-2 placeholder:text-black/40 focus:outline-none"
        />
        <button
          type="submit"
          onClick={onSearch}
          className="bg-primary p-2 rounded-full"
        >
          <Image
            priority
            src="/icons/search.svg"
            height={24}
            width={24}
            alt="search"
          />
        </button>
      </form>
      <div className="flex items-center space-x-4">
        {isAuthenticated ? (
          <div className="relative group">
            <Image
              src="/icons/profile.svg"
              alt="User"
              height={100}
              width={100}
              className="cursor-pointer max-w-[22px] max-h-[22px]"
            />
            <div className="absolute right-0 mt-2 w-40 bg-white border rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity">
              <Link href="/my-profile" className="block px-4 py-2 text-sm">
                My Profile
              </Link>
              <button
                onClick={() => {
                  localStorage.removeItem("token");
                  setIsAuthenticated(false);
                }}
                className="w-full text-left px-4 py-2 text-sm"
              >
                Logout
              </button>
            </div>
          </div>
        ) : (
          <Link href="/login">
            <Image
              src="/icons/profile.svg"
              alt="User"
              height={100}
              width={100}
              className="max-w-[22px] max-h-[22px]"
            />
          </Link>
        )}
        <CartBtn />
        <WishList />
      </div>
    </div>
  );
};

export default DesktopNavbar;
