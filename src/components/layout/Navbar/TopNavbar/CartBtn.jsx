"use client";

import { useAppSelector } from "@/lib/hooks/redux";
import Image from "next/image";
import Link from "next/link";
import React from "react";

const CartBtn = () => {
  const { cart } = useAppSelector((state) => state.carts);

  return (
    <Link href="/cart" className="relative p-1">
      <Image
        priority
        src="/icons/cart.svg"
        height={100}
        width={100}
        alt="cart"
        className="max-w-[22px] max-h-[22px]"
      />
      {cart && cart.items?.length > 0 && (
        <span className="border bg-black w-[18px] text-center text-white rounded-full w-fit-h-fit px-1 text-xs absolute -top-[0.6rem] left-[79%] -translate-x-1/2">
          {cart.items?.length}
        </span>
      )}
    </Link>
  );
};

export default CartBtn;
