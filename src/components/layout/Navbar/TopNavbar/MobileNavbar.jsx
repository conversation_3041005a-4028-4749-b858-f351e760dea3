import React from "react";
import Link from "next/link";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { integralCF } from "@/styles/fonts";
import { useSearch } from "@/lib/context/SearchContext";
import useDebounce from "@/lib/hooks/useDebounce";
import ResTopNavbar from "./ResTopNavbar";
import CartBtn from "./CartBtn";
import WishList from "./WishList";

import { useRouter } from "next/navigation";

const MobileNavbar = ({ data, isAuthenticated, setIsAuthenticated }) => {
  const [localSearchTerm, setLocalSearchTerm] = React.useState("");
  const { setSearchTerm } = useSearch();
  const router = useRouter();

  const debouncedSearchTerm = useDebounce((value) => setSearchTerm(value), 1000);

  const handleSearchChange = (e) => {
    setLocalSearchTerm(e.target.value);
    debouncedSearchTerm(e.target.value);
  };

  const onSearch = (e) => {
    e.preventDefault();
    router.push("/shop");
  };

  return (
    <div className="flex flex-col w-full">
      <div className="flex justify-between items-center">
        <ResTopNavbar data={data} />
        <Link
          href="/"
          className={cn([
            integralCF.className,
            "text-2xl lg:text-[32px] flex-shrink-0",
          ])}
        >
          <img src="/images/logo.png" alt="Logo" className="h-[35px]" />
        </Link>
        <div className="flex items-center space-x-2">
          {isAuthenticated ? (
            <div className="relative group">
              <Image
                src="/icons/profile.svg"
                alt="User"
                height={100}
                width={100}
                className="cursor-pointer max-w-[22px] max-h-[22px]"
              />
              <div className="absolute right-0 mt-2 w-40 bg-white border rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity">
                <Link href="/my-profile" className="block px-4 py-2 text-sm">
                  My Profile
                </Link>
                <button
                  onClick={() => {
                    localStorage.removeItem("token");
                    setIsAuthenticated(false);
                  }}
                  className="w-full text-left px-4 py-2 text-sm"
                >
                  Logout
                </button>
              </div>
            </div>
          ) : (
            <Link href="/login">
              <Image
                src="/icons/profile.svg"
                alt="User"
                height={100}
                width={100}
                className="max-w-[24px] max-h-[24px]"
              />
            </Link>
          )}
          <CartBtn />
          <WishList />
        </div>
      </div>
      <form className="flex bg-white shadow-extra w-full mt-3 rounded-full">
        <input
          type="search"
          placeholder="Search..."
          value={localSearchTerm}
          onChange={handleSearchChange}
          className="flex-grow px-4 py-2 placeholder:text-black/40 focus:outline-none"
        />
        <button
          onClick={onSearch}
          type="submit"
          className="bg-primary p-2 rounded-full"
        >
          <Image
            priority
            src="/icons/search.svg"
            height={24}
            width={24}
            alt="search"
          />
        </button>
      </form>
    </div>
  );
};

export default MobileNavbar;
