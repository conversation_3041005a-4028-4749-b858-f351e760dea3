import { cn } from "@/lib/utils";
import { integralCF } from "@/styles/fonts";
import Link from "next/link";
import React, { useState } from "react";

import { MenuList } from "./MenuList";
import {
  NavigationMenu,
  NavigationMenuList,
} from "@/components/ui/navigation-menu";
import { MenuItem } from "./MenuItem";
import Image from "next/image";
import CartBtn from "./CartBtn";
import WishList from "./WishList";
import ResTopNavbar from "./ResTopNavbar";
import { useRouter } from "next/navigation";
import { useSearch } from "@/lib/context/SearchContext";
import useDebounce from "@/lib/hooks/useDebounce";
import useAuth from "@/lib/hooks/useAuthenticated";

// Sample data for the navbar menu
const data = [
  {
    id: 1,
    type: "MenuItem",
    label: "Home",
    url: "/",
    children: [],
  },
  // {
  //   id: 2,
  //   label: "Kids",
  //   type: "MenuList",
  //   url: "/shop",
  //   children: [
  //     {
  //       id: 11,
  //       label: "Men's clothes",
  //       url: "/shop#men-clothes",
  //       description: "In attractive and spectacular colors and designs",
  //     },
  //     {
  //       id: 12,
  //       label: "Women's clothes",
  //       url: "/shop#women-clothes",
  //       description: "Ladies, your style and tastes are important to us",
  //     },
  //     {
  //       id: 13,
  //       label: "Kids clothes",
  //       url: "/shop#kids-clothes",
  //       description: "For all ages, with happy and beautiful colors",
  //     },
  //     {
  //       id: 14,
  //       label: "Bags and Shoes",
  //       url: "/shop#bag-shoes",
  //       description: "Suitable for men, women and all tastes and styles",
  //     },
  //   ],
  // },
  // {
  //   id: 3,
  //   label: "Stationary",
  //   type: "MenuList",
  //   url: "/shop",
  //   children: [
  //     {
  //       id: 11,
  //       label: "Men's clothes",
  //       url: "/shop#men-clothes",
  //       description: "In attractive and spectacular colors and designs",
  //     },
  //     {
  //       id: 12,
  //       label: "Women's clothes",
  //       url: "/shop#women-clothes",
  //       description: "Ladies, your style and tastes are important to us",
  //     },
  //     {
  //       id: 13,
  //       label: "Kids clothes",
  //       url: "/shop#kids-clothes",
  //       description: "For all ages, with happy and beautiful colors",
  //     },
  //     {
  //       id: 14,
  //       label: "Bags and Shoes",
  //       url: "/shop#bag-shoes",
  //       description: "Suitable for men, women and all tastes and styles",
  //     },
  //   ],
  // },
  // {
  //   id: 4,
  //   label: "GEN Z",
  //   type: "MenuList",
  //   url: "/shop",
  //   children: [
  //     {
  //       id: 11,
  //       label: "Men's clothes",
  //       url: "/shop#men-clothes",
  //       description: "In attractive and spectacular colors and designs",
  //     },
  //     {
  //       id: 12,
  //       label: "Women's clothes",
  //       url: "/shop#women-clothes",
  //       description: "Ladies, your style and tastes are important to us",
  //     },
  //     {
  //       id: 13,
  //       label: "Kids clothes",
  //       url: "/shop#kids-clothes",
  //       description: "For all ages, with happy and beautiful colors",
  //     },
  //     {
  //       id: 14,
  //       label: "Bags and Shoes",
  //       url: "/shop#bag-shoes",
  //       description: "Suitable for men, women and all tastes and styles",
  //     },
  //   ],
  // },
  {
    id: 2,
    type: "MenuItem",
    label: "Shop",
    url: "/shop",
    children: [],
  },
  {
    id: 3,
    type: "MenuItem",
    label: "Our Story",
    url: "/our-story",
    children: [],
  },
  {
    id: 4,
    type: "MenuItem",
    label: "Contact Us",
    url: "/contact-us",
    children: [],
  },
];

const TopNavbar = () => {
  const [localSearchTerm, setLocalSearchTerm] = useState(""); // To store search term locally
  const { searchTerm, setSearchTerm } = useSearch();
  const router = useRouter();
  const isAuthenticated = useAuth();
  const debouncedSearchTerm = useDebounce((value) => {
    setSearchTerm(value); // Update the global search term state
  }, 1000); // 1000ms debounce delay

  const handleSearchChange = (e) => {
    setLocalSearchTerm(e.target.value); // Update local state immediately for UI feedback
    debouncedSearchTerm(e.target.value); // Call the debounced function
  };

  const onSearch = (e) => {
    e.preventDefault();
    router.push("/shop"); // Redirect to the shop page without query params
  };

  return (
    <nav className="sticky top-0 bg-white shadow-sm z-20">
      <div className="flex flex-wrap items-center justify-between p-4 md:px-10 mx-auto">
        {/* Logo Section */}
        <div className="flex flex-col w-full md:flex-row md:items-center md:w-auto">
          <div className="flex items-center justify-between w-full">
            {/* Logo Section */}

            <div className="md:hidden">
              <ResTopNavbar data={data} />
            </div>
            <Link
              href="/"
              className={cn([
                integralCF.className,
                "text-2xl lg:text-[32px] flex-shrink-0 mr-4 md:m-0",
              ])}
            >
              <img
                src="/images/logo.png"
                alt="Logo"
                className="h-[35px] md:h-[40px]"
              />
            </Link>

            {/* User Icons and Cart/Wishlist */}
            <div className="md:hidden flex items-center space-x-2">
              {isAuthenticated ? (
                <div className="relative group">
                  {/* Profile Icon */}
                  <Image
                    src="/icons/profile.svg"
                    alt="User"
                    height={100}
                    width={100}
                    className="cursor-pointer max-w-[22px] max-h-[22px]"
                    onClick={() => router.push("/my-profile")}
                  />

                  {/* Dropdown Menu */}
                  {/* <div className="absolute right-0 mt-2 w-40 bg-white border rounded-lg shadow-lg opacity-0 pointer-events-none group-hover:opacity-100 group-hover:pointer-events-auto transition-opacity">
                    <Link
                      href="/my-profile"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      My Profile
                    </Link>
                    <button
                      onClick={() => {
                        localStorage.removeItem("token");
                        setIsAuthenticated(false);
                      }}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Logout
                    </button>
                  </div> */}
                </div>
              ) : (
                <Link href="/login">
                  <Image
                    src="/icons/profile.svg"
                    alt="User"
                    height={100}
                    width={100}
                    className="max-w-[22px] max-h-[22px]"
                  />
                </Link>
              )}
              <CartBtn />
              <WishList />
            </div>
          </div>

          {/* Mobile Search Bar */}
          <div className="flex bg-white shadow-extra w-full mt-3 max-w-full rounded-full overflow-hidden md:hidden">
            <input
              type="search"
              name="search"
              placeholder="Search..."
              value={localSearchTerm}
              onChange={handleSearchChange} // Use the debounced search
              className="flex-grow px-4 py-2 bg-white placeholder:text-black/40 focus:outline-none"
            />
            <button
              onClick={onSearch}
              className="flex items-center justify-center w-10 h-10 bg-primary rounded-full transition"
            >
              <Image
                priority
                src="/icons/search.svg"
                height={24}
                width={24}
                alt="search"
              />
            </button>
          </div>
        </div>

        {/* Desktop Navigation */}
        <NavigationMenu className="hidden md:flex mr-2 lg:mr-7">
          <NavigationMenuList>
            {data.map((item) => (
              <React.Fragment key={item.id}>
                {item.type === "MenuItem" && (
                  <MenuItem label={item.label} url={item.url} />
                )}
                {item.type === "MenuList" && (
                  <MenuList data={item.children} label={item.label} />
                )}
              </React.Fragment>
            ))}
          </NavigationMenuList>
        </NavigationMenu>

        {/* Desktop Search Bar */}
        <div className="hidden md:flex items-center space-x-4">
          <form className="bg-white shadow-extra w-[300px] flex rounded-full overflow-hidden">
            <input
              type="search"
              name="search"
              placeholder="Search Products..."
              value={localSearchTerm}
              onChange={handleSearchChange} // Use the debounced search
              className="flex-grow px-4 py-2 bg-white placeholder:text-black/40 focus:outline-none"
            />
            <button
              type="submit"
              onClick={onSearch}
              className="flex items-center justify-center w-10 h-10 bg-primary rounded-full transition"
            >
              <Image
                priority
                src="/icons/search.svg"
                height={24}
                width={24}
                alt="search"
              />
            </button>
          </form>
          <div className="flex items-center space-x-4">
            {isAuthenticated ? (
              <div className="relative group">
                {/* Profile Icon */}
                <Image
                  src="/icons/profile.svg"
                  alt="User"
                  height={100}
                  width={100}
                  className="cursor-pointer max-w-[22px] max-h-[22px]"
                  onClick={() => router.push("/my-profile")}
                />

                {/* Dropdown Menu */}
                {/* <div className="absolute right-0 mt-2 w-40 bg-white border rounded-lg shadow-lg opacity-0 pointer-events-none group-hover:opacity-100 group-hover:pointer-events-auto transition-opacity">
                  <Link
                    href="/my-profile"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    My Profile
                  </Link>
                  <button
                    onClick={() => {
                      localStorage.removeItem("token");
                      setIsAuthenticated(false);
                    }}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    Logout
                  </button>
                </div> */}
              </div>
            ) : (
              <Link href="/login">
                <Image
                  src="/icons/profile.svg"
                  alt="User"
                  height={100}
                  width={100}
                  className="max-w-[22px] max-h-[22px]"
                />
              </Link>
            )}
            <CartBtn />
            <WishList />
          </div>
        </div>
      </div>
    </nav>
  );
};

export default TopNavbar;
