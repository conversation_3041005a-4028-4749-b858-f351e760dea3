"use client";

import apiRoutes from "@/lib/constant";
import { useAppSelector } from "@/lib/hooks/redux";
import apiServiceWrapper from "@/lib/services/apiService";
import { cn } from "@/lib/utils";
import { me<PERSON><PERSON> } from "@/styles/fonts";
import { motion } from "framer-motion";
import { ArrowUp } from "lucide-react";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import {
  FaEnvelope,
  FaFacebookF,
  FaInstagram,
  FaPhoneAlt,
  FaTelegram,
  FaWhatsapp,
  FaYoutube,
} from "react-icons/fa";
import { FaLocationDot, FaThreads, FaXTwitter } from "react-icons/fa6";
import LayoutSpacing from "./LayoutSpacing";
import LinksSection from "./LinksSection";

const contactDetails = [
  {
    id: 1,
    icon: <FaLocationDot className="text-black" size={16} />,
    label: "Surat, Gujarat, India",
  },
  {
    id: 2,
    icon: <FaPhoneAlt className="text-black" size={16} />,
    label: "+91 72650 25193",
    href: "tel:+************",
  },
  {
    id: 3,
    icon: <FaEnvelope className="text-black" size={16} />,
    label: "<EMAIL>",
    href: "mailto:<EMAIL>",
  },
];

const Footer = () => {
  const [showScroll, setShowScroll] = useState(false);
  const [email, setEmail] = useState("");

  const { settings } = useAppSelector((state) => state.settings);
  const socialsData = [
    {
      id: 1,
      icon: <FaWhatsapp />,
      url: settings?.whatsapp_url || "#",
    },
    {
      id: 2,
      icon: <FaFacebookF />,
      url: settings?.facebook_url || "#",
    },
    {
      id: 3,
      icon: <FaInstagram />,
      url: settings?.instagram_url || "#",
    },
    {
      id: 4,
      icon: <FaThreads />,
      url: settings?.threads_url || "#",
    },
    {
      id: 5,
      icon: <FaTelegram />,
      url: settings?.telegram_url || "#",
    },
    {
      id: 6,
      icon: <FaYoutube />,
      url: settings?.youtube_url || "#",
    },
    {
      id: 7,
      icon: <FaXTwitter />,
      url: settings?.twitter_url || "#",
    },
  ];

  useEffect(() => {
    const handleScroll = () => {
      setShowScroll(window.scrollY > 300);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const pathname = usePathname();

  const handleSubmit = async () => {
    if (!email) {
      toast.dismiss();
      toast.error("Please enter a valid email.");
      return;
    }

    try {
      const response = await apiServiceWrapper.post(apiRoutes.SUBSCRIBE, {
        email,
      });
      if (!response.error) {
        setEmail("");
        toast.success("You have successfully subscribed to our newsletter!");
      }
    } catch (error) {
      // Safely access the error response message
      const errorMessage = error?.response?.data?.message;

      if (error?.response?.status === 422) {
        toast.error(errorMessage || "This email is already subscribed!");
      } else {
        toast.error(errorMessage || "Something went wrong. Please try again!");
      }
    }
  };

  return (
    <footer className="mt-10 relative overflow-hidden">
      <div className="pt-8 md:pt-[50px] bg-gradient-to-t from-[#EDFBFA] to-[#EDFBFA]/0 px-4 pb-4">
        <div className="max-w-frame mx-auto">
          <nav className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Section 1: Company Info */}
            <div className="flex flex-col">
              <h1 className={cn(merienda.className, "text-[40px] mb-3")}>
                CromiTopia
              </h1>
              <p className="text-black/60 text-sm">
                Shop adorable keychains, cute stationery, and must-have kawaii
                accessories! Bring joy, fun, and cuteness to your everyday life.
                ✨
              </p>
            </div>

            {/* Section 3: Links Section */}
            <div className="flex flex-col">
              <h2 className="text-xl font-semibold text-black mb-2">
                Quick Links
              </h2>
              <div className="h-1 w-10 bg-primary rounded-full mb-4" />
              <LinksSection />
            </div>

            {/* Section 2: Contact Information */}
            <div className="flex flex-col">
              <h2 className="text-xl font-semibold text-black mb-2">
                Contact Us
              </h2>
              <div className="h-1 w-10 bg-primary rounded-full mb-4" />
              <div className="space-y-4">
                {contactDetails.map((item) => (
                  <div key={item.id} className="flex items-center space-x-3">
                    <div className="bg-white w-9 h-9 rounded-full flex items-center justify-center shadow">
                      {item.icon}
                    </div>
                    {item.href ? (
                      <a
                        href={item.href}
                        className="text-black text-sm hover:underline transition-all"
                      >
                        {item.label}
                      </a>
                    ) : (
                      <p className="text-black text-sm">{item.label}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Section 4: Newsletter */}
            <div className="flex flex-col">
              <h2 className="text-xl font-semibold text-black mb-2">
                Subscribe Our Newsletter
              </h2>
              <div className="h-1 w-10 bg-primary rounded-full mb-4" />
              <p className="text-sm text-black/60 mb-4">
                With our newsletter, you&apos;ll never miss an important update.
              </p>
              <div className="p-1 flex items-center bg-white border border-gray-300 rounded-full overflow-hidden w-full">
                <div className="px-4">
                  <FaEnvelope className="text-gray-500" />
                </div>
                <input
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  type="email"
                  placeholder="Email Address"
                  className="flex-1 outline-none text-sm text-gray-700 bg-transparent placeholder:text-gray-500"
                />
                <button
                  onClick={handleSubmit}
                  className="bg-primary text-white p-3 rounded-full hover:bg-white hover:text-primary hover:border hover:border-primary transition-all"
                >
                  <ArrowUp className="rotate-45" size={16} />
                </button>
              </div>
              <div className="flex justify-center items-center mt-3 mb-16">
                {socialsData.map((social) => (
                  <a
                    href={social.url}
                    key={social.id}
                    target="_blank"
                    className="bg-white hover:bg-black hover:text-white transition-all mr-3 w-7 h-7 rounded-full border border-black/20 flex items-center justify-center p-1.5"
                  >
                    {social.icon}
                  </a>
                ))}
              </div>
            </div>
          </nav>

          {/* <hr className="h-[1px] border-t-black/10 my-6" /> */}
          {/* <div className="text-center">
            <p className="text-sm text-black/60">
              © {new Date().getFullYear()} CromiTopia. All rights reserved.
            </p>
          </div> */}
        </div>
        <LayoutSpacing />
      </div>
      <div className="relative w-full bg-white">
        {/* Background Shape */}
        <img
          src="/images/bottom.svg"
          alt="Footer Background"
          width={1920}
          height={400}
          className="w-full h-auto"
        />

        {/* Left Character */}
        <img
          src="/images/boy.svg"
          alt="Girl"
          width={150}
          height={150}
          className="absolute bottom-0 left-[10px] bottom-[10px] w-14 md:w-40"
        />

        {/* Right Character */}
        <img
          src="/images/girl.svg"
          alt="Boy"
          width={150}
          height={150}
          className="absolute bottom-0 right-[65px] md:right-[10px] bottom-[10px] w-14 md:w-32"
        />

        {/* Optional Floating Star */}
        <img
          src="/images/star.svg"
          alt="Star"
          width={24}
          height={24}
          className="absolute top-[-2.5rem] md:top-6 md:left-[20%] left-[30%] w-4 md:w-6 animate-bounce"
        />

        {/* Copyright Positioned */}
        <div className="hidden md:block">
          <p className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white text-sm text-center z-10">
            © {new Date().getFullYear()} CromiTopia. All rights reserved.
          </p>
          <p className="absolute bottom-2 left-1/2 transform -translate-x-1/2 text-white text-sm text-center z-10">
            Crafted and managed by{" "}
            <a
              target="_blank"
              href="https://instagram.com/innomi.tech"
              className="underline hover:text-primary"
            >
              InnoMi.tech
            </a>
          </p>
        </div>
      </div>

      {pathname === "/" && showScroll && (
        <motion.button
          onClick={scrollToTop}
          whileHover={{ scale: 1.2, rotate: 10 }}
          transition={{ type: "spring", stiffness: 300 }}
          className="fixed bottom-6 right-6 p-3 bg-primary text-white rounded-full shadow-lg hover:bg-white hover:border hover:border-primary hover:text-primary z-10"
        >
          <ArrowUp size={24} />
        </motion.button>
      )}
    </footer>
  );
};

export default Footer;
