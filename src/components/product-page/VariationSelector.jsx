import React from "react";
import { cn } from "@/lib/utils";
import Tooltip from "@/components/tooltip/Tooltip";

const VariationSelector = ({
  attributeSets,
  selectedAttributes,
  unavailableAttributeIds = [],
  onSelectVariation,
}) => {
  if (!attributeSets || attributeSets.length === 0) return null;

  return (
    <div className="space-y-6">
      {attributeSets.map((attributeSet) => (
        <div key={attributeSet.id} className="mb-2">
          <h3 className="text-lg font-bold text-gray-800">
            {attributeSet.title}
          </h3>

          {attributeSet.display_layout === "visual" ? (
            <div className="flex flex-wrap gap-2">
              {attributeSet.attributes.map((attribute) => {
                const isSelected = selectedAttributes.some(
                  (selected) => selected.id === attribute.id
                );
                const isUnavailable = unavailableAttributeIds.includes(
                  attribute.id
                );

                return (
                  <Tooltip
                    key={attribute.id}
                    content={attribute.title}
                    position="top"
                  >
                    <button
                      onClick={() =>
                        onSelectVariation(attributeSet.id, attribute.id)
                      }
                      disabled={isUnavailable}
                      className={cn(
                        "w-8 h-8 rounded-full border-2 transition-all flex items-center justify-center",
                        isSelected
                          ? "border-primary shadow-lg scale-105"
                          : "border-gray-300 hover:border-primary",
                        isUnavailable && "opacity-50 cursor-not-allowed"
                      )}
                      aria-label={attribute.title}
                    >
                      {attribute.color ? (
                        <span
                          className="w-6 h-6 rounded-full"
                          style={{ backgroundColor: attribute.color }}
                        />
                      ) : attribute.image ? (
                        <img
                          src={attribute.image}
                          alt={attribute.title}
                          className="w-6 h-6 rounded-full object-cover"
                        />
                      ) : (
                        <span className="text-xs">{attribute.title}</span>
                      )}
                    </button>
                  </Tooltip>
                );
              })}
            </div>
          ) : (
            <div className="flex flex-wrap gap-2">
              {attributeSet.attributes.map((attribute) => {
                const isSelected = selectedAttributes.some(
                  (selected) => selected.id === attribute.id
                );
                const isUnavailable = unavailableAttributeIds.includes(
                  attribute.id
                );

                return (
                  <Tooltip
                    key={attribute.id}
                    content={attribute.title}
                    position="top"
                  >
                    <button
                      onClick={() =>
                        onSelectVariation(attributeSet.id, attribute.id)
                      }
                      disabled={isUnavailable}
                      className={cn(
                        "px-2 py-1 rounded-lg border-2 transition-all text-xs font-medium",
                        isSelected
                          ? "border-primary bg-primary/10 text-primary shadow-lg"
                          : "border-gray-300 hover:border-primary text-gray-700",
                        isUnavailable && "opacity-50 cursor-not-allowed"
                      )}
                      aria-label={attribute.title}
                    >
                      {attribute.title}
                    </button>
                  </Tooltip>
                );
              })}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default VariationSelector;

