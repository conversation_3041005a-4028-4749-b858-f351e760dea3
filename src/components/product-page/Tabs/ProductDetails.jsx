import React from "react";

const ProductDetails = ({ data }) => {
  return (
    <>
      {data?.product_spec?.length > 0 &&
        data?.product_spec.map((item, i) => (
          <div className="grid grid-cols-3" key={i}>
            <div>
              <p className="text-sm py-3 w-full leading-7 lg:py-4 pr-2 text-neutral-500">
                {item.key}
              </p>
            </div>
            <div className="col-span-2 py-3 lg:py-4 border-b">
              <p className="text-sm w-full leading-7 text-neutral-800 font-medium">
                {item.value}
              </p>
            </div>
          </div>
        ))}
    </>
  );
};

export default ProductDetails;
