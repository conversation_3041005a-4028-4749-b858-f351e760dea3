import React from "react";
import {
  Bread<PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import Link from "next/link";

const BreadcrumbProduct = ({ title, category = null }) => {
  const seoData = [
    { name: "Home", url: "/" },
    { name: "Shop", url: "/shop" },
  ];

  if (category) {
    seoData.push({
      name: category.name,
      url: `/shop?categories=${category.slug}`,
    });
  }

  seoData.push({ name: title, url: null });

  return (
    <Breadcrumb seoData={seoData} className="mb-5 sm:mb-9">
      <BreadcrumbList>
        <BreadcrumbItem position="1">
          <BreadcrumbLink asChild>
            <Link href="/">Home</Link>
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem position="2">
          <BreadcrumbLink asChild>
            <Link href="/shop">Shop</Link>
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        {category && (
          <>
            <BreadcrumbItem position="3">
              <BreadcrumbLink asChild>
                <Link href={`/shop?categories=${category.slug}`}>
                  {category.name}
                </Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
          </>
        )}
        <BreadcrumbItem position={category ? "4" : "3"}>
          <BreadcrumbPage>{title}</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );
};

export default BreadcrumbProduct;
