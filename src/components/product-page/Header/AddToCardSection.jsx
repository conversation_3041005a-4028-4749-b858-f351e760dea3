"use client";

import CartCounter from "@/components/ui/CartCounter";
import React, { useEffect, useState } from "react";
import AddToCartBtn from "./AddToCartBtn";

import { useDispatch } from "react-redux";
import {
  addToCart,
  remove,
} from "@/lib/features/carts/cartsSlice";
import { useAppSelector } from "@/lib/hooks/redux";
import { TrashIcon } from "lucide-react";
import toast from "react-hot-toast";

const AddToCardSection = ({ data }) => {
  const [quantity, setQuantity] = useState(1);
  const dispatch = useDispatch();
  const { cart } = useAppSelector((state) => state.carts);

  // Check if the item is already in the cart
  const isItemInCart = cart?.items.some((item) => item.id === data.id);

  // Update quantity based on the cart state from Redux
  useEffect(() => {
    const cartItem = cart?.items?.find((item) => item.id === data.id);
    if (cartItem && cartItem.quantity !== quantity) {
      setQuantity(cartItem.quantity);
    }
  }, [cart?.items, data.id]);

  const updateQuantity = (newQuantity) => {
    if (newQuantity < 1) return; // Prevent invalid quantities

    // Check if the desired quantity exceeds stock
    if (newQuantity > data.quantity) {
      toast.dismiss();
      toast.error(`Only ${data.quantity} items are in stock.`); // Show a message to the user
      return;
    }

    // Update the local state first
    setQuantity(newQuantity);

    // Dispatch action to update the cart in Redux
    dispatch(
      addToCart({
        id: data.id,
        name: data.name,
        srcUrl: data.image_urls?.[0] || "",
        price: data.selling_price,
        original_price: data.original_price,
        attributes: [],
        discount: data.discount,
        quantity: newQuantity,
        slug: data.slug,
        description: data.description,
      })
    );
  };

  const removeItemFromCart = () => {
    setQuantity(1);
    dispatch(
      remove({
        id: data.id,
        attributes: [],
        quantity: 0,
      })
    ); // Adjust attributes if necessary
  };

  return (
    <div className="fixed md:relative w-full bg-white border-t md:border-none border-black/5 bottom-0 left-0 p-4 md:p-0 z-10 flex items-center justify-between sm:justify-start md:justify-center">
      {/* Pass the quantity and updateQuantity props to CartCounter */}
      <CartCounter updateQuantity={updateQuantity} quantity={quantity} />

      <AddToCartBtn data={{ ...data, quantity }} />

      <div className="flex items-center space-x-3">
        {/* Conditionally render Remove from Cart button */}
        {isItemInCart && (
          <button
            onClick={removeItemFromCart}
            className="bg-red-500 text-white px-4 py-3 mx-2 rounded-lg hover:bg-red-600 transition-colors"
          >
            <TrashIcon />
          </button>
        )}
      </div>
    </div>
  );
};

export default AddToCardSection;
