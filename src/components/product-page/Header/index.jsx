import React from "react";
import PhotoSection from "./PhotoSection";
import { integralCF } from "@/styles/fonts";
import { cn } from "@/lib/utils";
import AddToCardSection from "./AddToCardSection";

const Header = ({ data }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      <div>
        <PhotoSection data={data} />
      </div>
      <div className="p-4 md:p-6">
        <h1
          className={cn([
            integralCF.className,
            "text-3xl md:text-4xl font-bold text-gray-800 capitalize mb-4",
          ])}
        >
          {data.name}
        </h1>
        <div className="flex items-center space-x-3 mb-4">
          <span className="text-2xl font-bold text-primary">
            ₹{data.selling_price}
          </span>
          {parseFloat(data.original_price) > parseFloat(data.selling_price) && (
            <>
              <span className="text-lg text-gray-400 line-through">
                ₹{data.original_price}
              </span>
              <span className="text-sm font-medium text-red-500 bg-red-100 px-3 py-1 rounded-full">
                {`-${Math.round(
                  ((data.original_price - data.selling_price) /
                    data.original_price) *
                    100,
                )}%`}
              </span>
            </>
          )}
        </div>
        <p className="text-gray-600 text-sm md:text-base leading-relaxed mb-6">
          {data.description}
        </p>
        <AddToCardSection data={data} />
      </div>
    </div>
  );
};

export default Header;
