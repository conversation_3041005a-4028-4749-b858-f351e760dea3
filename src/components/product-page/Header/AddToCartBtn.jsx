"use client";

import { addToCart } from "@/lib/features/carts/cartsSlice";
import { useAppDispatch } from "@/lib/hooks/redux";
import React from "react";

const AddToCartBtn = ({ data }) => {
  const dispatch = useAppDispatch();

  return (
    <button
      type="button"
      className="bg-black w-full ml-3 sm:ml-5 rounded-full h-11 md:h-[52px] text-sm sm:text-base text-white hover:bg-black/80 transition-all"
      onClick={() =>
        dispatch(
          addToCart({
            id: data.id,
            name: data.name,
            srcUrl: data.image_urls?.[0] || "",
            price: data.selling_price,
            original_price: data.original_price,
            attributes: [],
            discount: data.discount,
            quantity: data.quantity,
            slug: data.slug,
            description: data.description,
          }),
        )
      }
    >
      Add to Cart
    </button>
  );
};

export default AddToCartBtn;
