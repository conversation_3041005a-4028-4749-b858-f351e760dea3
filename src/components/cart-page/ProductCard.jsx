"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";
import CartCounter from "@/components/ui/CartCounter";
import {
  addToCart,
  removeCartItem,
  removeCartItemAsync,
} from "@/lib/features/carts/cartsSlice";
import { useAppDispatch } from "@/lib/hooks/redux";
import { removeFromWishlistApi } from "@/lib/features/wishlist/wishlistSlice";
import toast from "react-hot-toast";

const ProductCard = ({ data, isWishList = false }) => {
  const dispatch = useAppDispatch();
  const [quantity, setQuantity] = useState(data.quantity);

  const updateQuantity = (newQuantity) => {
    if (newQuantity < 1) return; // Prevent invalid quantities
    setQuantity(newQuantity);
    // Dispatch action to update the cart in Redux
    dispatch(addToCart({ ...data, quantity: newQuantity }));
  };

  const removeItem = () => {
    if (isWishList) {
      dispatch(
        removeFromWishlistApi({
          id: data.id,
          attributes: [],
        })
      );
    } else {
      toast.success("Removed from cart!");
      dispatch(
        removeCartItem({
          id: data.id,
        })
      );
      dispatch(
        removeCartItemAsync({
          id: data.id,
        })
      );
    }
  };
  const hasDiscount = parseFloat(data.original_price) > parseFloat(data.price);
  return (
    <div className="flex items-start space-x-4">
      <Link
        href={`/shop/product/${data.slug}`}
        className="bg-[#F0EEED] rounded-lg w-full min-w-[100px] max-w-[100px] sm:max-w-[124px] aspect-square overflow-hidden"
      >
        <img
          src={data.image_with_sizes?.origin[0]}
          width={124}
          height={124}
          className="rounded-md w-full h-full object-cover hover:scale-110 transition-all duration-500"
          alt={data.name}
        />
      </Link>
      <div className="flex w-full self-stretch flex-col">
        <div className="flex items-center justify-between">
          <div>
            <Link
              href={`/shop/product/${data.slug}`}
              className="text-black font-bold text-base xl:text-xl"
            >
              {data.name}
            </Link>
            <div
              className="text-black/60 text-xs md:text-sm line-clamp-2"
              dangerouslySetInnerHTML={{ __html: data.description }}
            ></div>
          </div>
          <div>
            <button
              className="flex items-end justify-end w-16 p-2" // Adjust size and padding
              onClick={() => removeItem()}
            >
              <img
                src="/icons/delete.svg"
                alt="delete"
                className="w-6 h-6 sm:w-6 sm:h-6" // Adjust icon size
              />
            </button>
          </div>
        </div>
        {/* <div className="-mt-1">
          <span className="text-black text-xs md:text-sm mr-1">Size:</span>
          <span className="text-black/60 text-xs md:text-sm">
            {data.attributes[0]}
          </span>
        </div>
        <div className="mb-auto -mt-1.5">
          <span className="text-black text-xs md:text-sm mr-1">Color:</span>
          <span className="text-black/60 text-xs md:text-sm">
            {data.attributes[1]}
          </span>
        </div> */}
        <div className="flex items-center mt-5 flex-wrap justify-between">
          <div className="flex items-center space-x-[5px]">
            <span className="font-bold text-black text-xl">
              {data.price_formatted}
            </span>
            {hasDiscount && (
              <span className="text-xl text-xs sm:text-sm line-through text-gray-400">
                {data.original_price_formatted}
              </span>
            )}
          </div>
          {isWishList ? null : (
            <CartCounter
              updateQuantity={updateQuantity}
              quantity={quantity}
              className="mt-5 md:mt-0 px-5 py-3 max-h-8 md:max-h-10 min-w-[105px] max-w-[105px] sm:max-w-32"
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
