import React from "react";
import {
  Bread<PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import Link from "next/link";

const BreadcrumbCart = () => {
  const seoData = [
    { name: "Home", url: "/" },
    { name: "Cart", url: "/cart" },
  ];

  return (
    <Breadcrumb seoData={seoData} className="mb-2 sm:mb-6">
      <BreadcrumbList>
        <BreadcrumbItem position="1">
          <BreadcrumbLink asChild>
            <Link href="/">Home</Link>
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem position="2">
          <BreadcrumbPage>Cart</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );
};

export default BreadcrumbCart;
