import CryptoJS from "crypto-js";
const SECRET_KEY = process.env.NEXT_PUBLIC_SECRET;

export const decryptData = (encryptedData) => {
  try {
    // First, parse the base64 encoded JSONß)
    const parsedData = JSON.parse(atob(encryptedData));

    // Extract the key (removing 'base64:' prefix)
    const keyPart = SECRET_KEY.substr(7);

    // Convert IV and key from base64
    const iv = CryptoJS.enc.Base64.parse(parsedData.iv);
    const key = CryptoJS.enc.Base64.parse(keyPart);

    // Correct way to decrypt with CryptoJS
    const cipherParams = CryptoJS.lib.CipherParams.create({
      ciphertext: CryptoJS.enc.Base64.parse(parsedData.value),
    });

    const decryptedBytes = CryptoJS.AES.decrypt(cipherParams, key, {
      iv: iv,
      padding: CryptoJS.pad.Pkcs7,
      mode: CryptoJS.mode.CBC,
    });

    // Convert to string
    const decryptedText = decryptedBytes.toString(CryptoJS.enc.Utf8);

    // Handle PHP serialized format if present
    if (decryptedText.startsWith("s:")) {
      // Extract the JSON array from the serialized format
      // Format is s:148:"[{...}]"; - we need to extract what's inside the quotes
      const match = decryptedText.match(/s:\d+:"(.+)";/);
      if (match && match[1]) {
        return match[1];
      }
      throw new Error("Could not extract data from serialized format");
    }

    return decryptedText;
  } catch (error) {
    console.error("Error decrypting data:", error);
  }
};
