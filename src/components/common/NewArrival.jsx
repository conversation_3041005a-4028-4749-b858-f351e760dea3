"use client";

import React from "react";
import * as motion from "framer-motion/client";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import Link from "next/link";

import SpinnerbLoader from "../ui/SpinnerbLoader";

const NewArrival = ({
  title,
  viewAllLink = "/",
  products,
  loading = false,
  error = null,
  buttonText = "View All",
  isProductDetails = false,
}) => {
  return (
    <section
      className={`max-w-frame mx-auto text-center mt-[25px] ${
        isProductDetails ? "mb-[98px]" : ""
      }`}
    >
      <motion.h2
        initial={{ y: "100px", opacity: 0 }}
        whileInView={{ y: "0", opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6 }}
        className="text-[32px] md:text-5xl mb-8 md:mb-14 capitalize font-bold"
      >
        {title}
      </motion.h2>

      {true ? (
        <SpinnerbLoader className="w-10 border-2 border-gray-300 border-r-gray-600" />
      ) : error ? (
        <p className="text-lg font-semibold text-red-500">{error}</p>
      ) : (
        <motion.div
          initial={{ y: "100px", opacity: 0 }}
          whileInView={{ y: "0", opacity: 1 }}
          viewport={{ once: true }}
          transition={{ delay: 0.6, duration: 0.6 }}
        >
          <Carousel opts={{ align: "start" }} className="w-full mb-6 md:mb-9">
            <CarouselContent className="mx-4 xl:mx-0 flex">
              {products &&
                products?.length > 0 &&
                products.map((product) => (
                  <CarouselItem
                    key={product.id}
                    className="w-1/5 max-w-[240px] flex flex-col items-center cursor-pointer"
                  >
                    <Link href={`/shop/product/${product.slug}`}>
                      {/* Product Card */}
                      <div className="relative w-full max-w-[220px] p-4 group bg-white rounded-[11px] border border-dashed border-[#999999] overflow-hidden">
                        {/* Discount Badge */}
                        {product.discount && (
                          <span className="absolute top-2 left-2 bg-black text-white text-xs px-2 py-1 rounded-md">
                            {product.discount}%
                          </span>
                        )}

                        {/* Image with Hover Effect */}
                        <div className="relative w-full h-48 flex items-center justify-center overflow-hidden">
                          <img
                            src={product.image_urls?.[0]}
                            alt={product.name}
                            className={`object-cover w-full h-full transition-opacity duration-300 ease-in-out ${
                              product.image_urls?.length > 1
                                ? "group-hover:opacity-0"
                                : ""
                            }`}
                          />
                          {product.image_urls?.length > 1 && (
                            <img
                              src={product.image_urls?.[1]}
                              alt={product.name}
                              className="absolute top-0 left-0 w-full h-full object-cover opacity-0 transition-opacity duration-300 ease-in-out group-hover:opacity-100"
                            />
                          )}
                        </div>
                      </div>

                      {/* Product Info */}
                      <p className="mt-2 text-lg font-semibold">
                        {product.name}
                      </p>
                      <div className="flex items-center gap-2 mt-1 text-gray-700">
                        <span className="text-black font-bold">
                          Rs. {product.selling_price}
                        </span>
                        {product.original_price && (
                          <span className="line-through text-gray-500 text-sm">
                            Rs. {product.original_price}
                          </span>
                        )}
                      </div>

                      {/* Color Variants */}
                      {/* {product.colors?.length > 0 && (
                    <div className="flex gap-1 mt-2">
                      {product.colors.map((color, index: number) => (
                        <div
                          key={index}
                          className="w-5 h-5 rounded-full border border-gray-300"
                          style={{ backgroundColor: color }}
                        ></div>
                      ))}
                    </div>
                  )} */}
                    </Link>
                  </CarouselItem>
                ))}
            </CarouselContent>
          </Carousel>

          {/* View All Button */}
          {viewAllLink && (
            <div className="w-full px-4 sm:px-0 text-center">
              <Link
                href={viewAllLink}
                className="inline-block px-6 py-3 relative text-black font-medium text-sm sm:text-base transition-all font-medium text-sm sm:text-base rounded-full shadow-lg"
              >
                {buttonText}
                <span className="ml-2 inline-block animate-bounce-horizontal">
                  →
                </span>
              </Link>
            </div>
          )}
        </motion.div>
      )}
    </section>
  );
};

export default NewArrival;
