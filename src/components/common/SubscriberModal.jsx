"use client";

import React, { useEffect, useState } from "react";
import Cookies from "js-cookie";
import { X } from "lucide-react";
import { useAppSelector, useAppDispatch } from "@/lib/hooks/redux";
import apiServiceWrapper from "@/lib/services/apiService";
import toast from "react-hot-toast";
import apiRoutes from "@/lib/constant";

const SubscriberModal = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [email, setEmail] = useState("");
  const { subscriberModalSettings } = useAppSelector((state) => state.settings);
  const dispatch = useAppDispatch();

  useEffect(() => {
    const hasSubscribed = Cookies.get("subscriber_modal");

    if (!hasSubscribed) {
      // Open the modal after 5 seconds if not already subscribed
      setTimeout(() => {
        setIsOpen(true);
        // dispatch(fetchSubscriberModalSettings());
      }, 5000);
    }
  }, [dispatch]);

  const handleClose = () => {
    Cookies.set("subscriber_modal", "true", { expires: 2 }); // Set cookie for 2 days
    setIsOpen(false);
  };

  const handleSubmit = async () => {
    if (!email) {
      toast.error("Please enter a valid email.");
      return;
    }

    try {
      const response = await apiServiceWrapper.post(apiRoutes.SUBSCRIBE, {
        email,
      });
      if (!response.error) {
        Cookies.set("subscriber_modal", "true", { expires: 15 });
        toast.success("You have successfully subscribed to our newsletter!");
        setIsOpen(false);
      }
    } catch (error) {
      toast.error(error.response?.data?.message || "An error occurred.");
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <div className="bg-white w-full max-w-[90%] md:max-w-3xl rounded-lg shadow-lg overflow-hidden relative">
        {/* Close Button */}
        <button
          className="absolute top-4 right-4 z-10 text-gray-600 hover:text-black"
          onClick={handleClose}
        >
          <X size={24} className="text-white md:text-black" />
        </button>

        <div className="grid grid-cols-1 md:grid-cols-2">
          {/* Left Side - Image */}
          <div className="relative w-full h-64 md:h-auto">
            <img
              src={"/images/waiting.webp"}
              alt="Subscribe Now"
              className="w-full h-full object-cover rounded-t-lg md:rounded-l-lg md:rounded-t-none"
            />
          </div>

          {/* Right Side - Text & Form */}
          <div className="p-6 flex flex-col justify-center items-center text-center">
            <h2 className="text-2xl font-bold mb-3">
              {subscriberModalSettings?.modal_title ??
                "Hey Cutie, Wanna Be First? 👀💌"}
            </h2>
            <p className="text-gray-600 mb-4">
              {subscriberModalSettings?.modal_description ??
                "Subscribe and get early access to kawaii goodies, exclusive deals, and magical surprises you won’t find anywhere else. ✨"}
            </p>
            <div className="w-full">
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                className="w-full px-4 py-2 border rounded-md mb-3"
              />
              <button
                onClick={handleSubmit}
                className="bg-primary text-white px-6 py-2 rounded-md hover:bg-primary/90 transition w-full"
              >
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriberModal;
