import React from "react";
import CategoriesSection from "@/components/shop-page/filters/CategoriesSection";
import ColorsSection from "@/components/shop-page/filters/ColorsSection";
import DressStyleSection from "@/components/shop-page/filters/DressStyleSection";
import PriceSection from "@/components/shop-page/filters/PriceSection";
import SizeSection from "@/components/shop-page/filters/SizeSection";
import { Button } from "@/components/ui/button";

const Filters = ({
  selectedCategory,
  setSelectedCategory,
  activeFilter,
  setActiveFilter,
  updateURL,
  setCurrentPage,
  onFilterChange,
  isMobile = false,
}) => {
  const filterOptions = [
    { label: "Trending", value: "4" },
    { label: "New Arrival", value: "5" },
    { label: "Most Selling", value: "6" },
    { label: "Most Viewed", value: "7" },
  ];
  return (
    <>
      <hr className="border-t-black/10" />
      <div className="flex flex-wrap items-center space-x-3">
        <div className="grid grid-cols-2 gap-2">
          {filterOptions.map((filter, index) => (
            <button
              key={filter.value}
              className={`text-xs sm:text-sm font-medium px-4 py-2 rounded-lg ${
                activeFilter == filter.value
                  ? "text-white bg-black hover:bg-gray-800"
                  : "text-gray-700 bg-gray-200 hover:bg-gray-300"
              } transition`}
              onClick={() => {
                const handleClick = () => {
                  if (activeFilter == filter.value) {
                    setActiveFilter(""); // Deselect filter if already selected
                    setSelectedCategory(null);
                    updateURL({ filter: null, categories: null, page: 1 });
                  } else {
                    setActiveFilter(filter.value);
                    setSelectedCategory(null);
                    updateURL({
                      filter: filter.value,
                      categories: null,
                      page: 1,
                    });
                  }
                  setCurrentPage(1);
                };

                if (isMobile && onFilterChange) {
                  onFilterChange(handleClick);
                } else {
                  handleClick();
                }
              }}
            >
              {filter.label}
            </button>
          ))}
        </div>
      </div>

      <CategoriesSection
        selectedCategory={selectedCategory}
        setSelectedCategory={setSelectedCategory}
        updateURL={updateURL}
        setCurrentPage={setCurrentPage}
        onFilterChange={onFilterChange}
        isMobile={isMobile}
      />
      {/* <hr className="border-t-black/10" /> */}
      {/* <PriceSection /> */}
      {/* <hr className="border-t-black/10" />
      <ColorsSection /> */}
      {/* <hr className="border-t-black/10" />
      <SizeSection /> */}
      {/* <hr className="border-t-black/10" />
      <DressStyleSection /> */}
      {/* <Button
        type="button"
        className="bg-black w-full rounded-full text-sm font-medium py-4 h-12"
      >
        Apply Filter
      </Button> */}
    </>
  );
};

export default Filters;
