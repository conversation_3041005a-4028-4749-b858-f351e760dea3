"use client";

import { useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { usePathname } from "next/navigation";
import TopNavbar from "@/components/layout/Navbar/TopNavbar";
import Footer from "@/components/layout/Footer";
import SubscriberModal from "@/components/common/SubscriberModal";
import { fetchSettings } from "@/lib/features/settings/settingSlice";
import { useAppDispatch, useAppSelector } from "@/lib/hooks/redux";

export default function ClientLayout({ children }) {
  const pathname = usePathname();
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { settings } = useAppSelector((state) => state.settings);
  const previousPathname = useRef(pathname);

  const disableInspect = (e) => {
    // Disable right-click
    if (e.type === "contextmenu") {
      e.preventDefault();
    }

    // Disable F12 and other inspect shortcuts
    if (e.type === "keydown") {
      if (
        e.key === "F12" ||
        (e.ctrlKey && e.shiftKey && ["I", "C", "J"].includes(e.key)) ||
        (e.ctrlKey && e.key === "u")
      ) {
        e.preventDefault();
      }
    }
  };

  useEffect(() => {
    if (process.env.NODE_ENV === "production") {
      const params = new URLSearchParams(window.location.search);
      const isInspect = params.get("is_inspect") === "true";

      if (!isInspect) {
        // Block inspect functionality
        document.addEventListener("contextmenu", disableInspect);
        document.addEventListener("keydown", disableInspect);
      }
    }

    // Cleanup on component unmount or when the parameter changes
    return () => {
      document.removeEventListener("contextmenu", disableInspect);
      document.removeEventListener("keydown", disableInspect);
    };
  }, []);

  useEffect(() => {
    if (settings && Object.keys(settings)?.length > 0) return;
    dispatch(fetchSettings());
  }, [dispatch]);

  // Define routes that require authentication
  const protectedRoutes = ["/addresses", "/orders", "/my-profile", "/reviews"];

  useEffect(() => {
    // Check if the current route is protected
    if (protectedRoutes.includes(pathname)) {
      const token = localStorage.getItem("token");

      if (!token) {
        // Redirect to login if token is not present
        router.push("/");
      }
    }
  }, [pathname, router]);

  useEffect(() => {
    // Only scroll to top if we're actually changing routes (not just URL params)
    // and avoid scrolling for certain navigation patterns
    if (previousPathname.current !== pathname) {
      // Don't auto-scroll for shop/category pages as they handle their own scroll behavior
      const isShopOrCategory =
        pathname.includes("/shop") || pathname.includes("/category");
      const wasShopOrCategory =
        previousPathname.current.includes("/shop") ||
        previousPathname.current.includes("/category");

      // Only auto-scroll if we're not navigating between shop/category pages
      // or if we're going from shop/category to home page
      if (!isShopOrCategory || (!wasShopOrCategory && pathname === "/")) {
        // Use a small delay to ensure the page has rendered
        const timeoutId = setTimeout(() => {
          window.scrollTo({ top: 0, behavior: "instant" });
        }, 50);

        // Update the previous pathname
        previousPathname.current = pathname;

        return () => clearTimeout(timeoutId);
      } else {
        // Just update the previous pathname without scrolling
        previousPathname.current = pathname;
      }
    }
  }, [pathname]);

  // Handle browser back/forward navigation scroll restoration
  useEffect(() => {
    // Disable automatic scroll restoration by the browser
    if ("scrollRestoration" in history) {
      history.scrollRestoration = "manual";
    }

    return () => {
      // Restore default behavior on cleanup
      if ("scrollRestoration" in history) {
        history.scrollRestoration = "auto";
      }
    };
  }, []);

  // Determine if layout should be hidden for specific routes
  const hideLayout = pathname === "/register" || pathname === "/login";

  if (hideLayout) {
    return <>{children}</>; // only render the page content
  }

  if (pathname.includes("shop/produc") || pathname.includes("addresses")) {
    return (
      <>
        <TopNavbar />
        {children}
      </>
    ); // render without footer for shop/product pages
  }

  return (
    <>
      <SubscriberModal />
      <TopNavbar />
      {children}
      <Footer />
    </>
  );
}
