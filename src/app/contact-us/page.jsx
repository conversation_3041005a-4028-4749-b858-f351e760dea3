"use client";

"use client";

import apiRoutes from "@/lib/constant";
import apiServiceWrapper from "@/lib/services/apiService";
import { useState } from "react";
import toast from "react-hot-toast";
import { FiUser, FiMail, FiMessageCircle } from "react-icons/fi";

export default function ContactUsPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    content: "",
  });
  const [loading, setLoading] = useState(false);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const validateForm = () => {
    const { name, email, subject, content } = formData;
    if (!name) {
      toast.dismiss();
      toast.error("Name is required.");
      return false;
    }
    if (!email) {
      toast.dismiss();
      toast.error("Email is required.");
      return false;
    }
    if (!subject) {
      toast.dismiss();
      toast.error("Subject is required.");
      return false;
    }
    if (!content) {
      toast.dismiss();
      toast.error("Message is required.");
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    toast.dismiss();

    if (!validateForm()) return;

    setLoading(true);

    try {
      const response = await apiServiceWrapper.post(
        apiRoutes.CONTACT,
        formData
      );
      if (!response.error) {
        toast.success("Message sent successfully!");
        setFormData({ name: "", email: "", subject: "", content: "" });
      }
    } catch (error) {
      toast.dismiss();
      toast.error("Failed to send your content. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-cyan-100 to-blue-200 bg-no-repeat bg-cover"
      style={{ backgroundImage: 'url("/cartoon-background.jpg")' }}
    >
      <head>
        <title>Contact Us | CromiTopia</title>
        <meta name="description" content="Contact us for support" />
      </head>
      <div className="bg-white bg-opacity-95 shadow-lg rounded-xl p-8 w-full max-w-md relative z-10">
        <h1 className="text-4xl font-semibold text-center mb-6 text-[#407b82]">
          Contact Us
        </h1>
        <p className="text-center text-gray-600 mb-6">
          We’re here to help! Fill out the form below and we’ll get back to you
          soon.
        </p>

        <form onSubmit={handleSubmit} className="space-y-5">
          <div className="relative">
            <FiUser className="absolute left-3 top-3 text-[#407b82]" />
            <input
              name="name"
              type="text"
              placeholder="Your Name"
              value={formData.name}
              onChange={handleChange}
              className="w-full pl-10 border-gray-300 rounded-lg px-4 py-3 focus:ring-[#407b82] focus:outline-none"
              required
            />
          </div>

          <div className="relative">
            <FiMail className="absolute left-3 top-3 text-[#407b82]" />
            <input
              name="email"
              type="email"
              placeholder="Your Email"
              value={formData.email}
              onChange={handleChange}
              className="w-full pl-10 border-gray-300 rounded-lg px-4 py-3 focus:ring-[#407b82] focus:outline-none"
              required
            />
          </div>

          <div className="relative">
            <FiMessageCircle className="absolute left-3 top-3 text-[#407b82]" />
            <input
              name="subject"
              type="text"
              placeholder="Subject"
              value={formData.subject}
              onChange={handleChange}
              className="w-full pl-10 border-gray-300 rounded-lg px-4 py-3 focus:ring-[#407b82] focus:outline-none"
              required
            />
          </div>

          <div className="relative">
            <textarea
              name="content"
              placeholder="Your Message"
              value={formData.content}
              onChange={handleChange}
              rows={4}
              className="w-full border-gray-300 rounded-lg px-4 py-3 focus:ring-[#407b82] focus:outline-none"
              required
            />
          </div>

          <button
            type="submit"
            className={`w-full py-3 rounded-lg transition-all text-white ${
              loading
                ? "bg-gray-400 cursor-not-allowed"
                : "bg-[#407b82] hover:bg-[#35656b]"
            }`}
            disabled={loading}
          >
            {loading ? "Sending..." : "Send Message"}
          </button>
        </form>
      </div>

      {/* Decorative Cartoon Shapes */}
      <div className="absolute top-10 left-10 w-20 h-20 bg-[#407b82] rounded-full opacity-50"></div>
      <div className="absolute bottom-10 right-10 w-20 h-20 bg-[#35656b] rounded-full opacity-50"></div>
    </div>
  );
}
