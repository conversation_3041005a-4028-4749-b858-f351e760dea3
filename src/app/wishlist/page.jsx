"use client";

import React, { useEffect, useState } from "react";
import {
  X,
  <PERSON>,
  Star,
  Grid,
  List,
  ChevronDown,
  Filter,
} from "lucide-react";
import toast from "react-hot-toast";
import Link from "next/link";
import { useAppDispatch, useAppSelector } from "@/lib/hooks/redux";
import {
  addToWishlistApi,
  removeFromWishlistApi,
} from "@/lib/features/wishlist/wishlistSlice";
import { addToCart, addToCartAsync } from "@/lib/features/carts/cartsSlice";

const WishlistPage = () => {
  const [viewMode, setViewMode] = useState("grid");
  const [sortBy, setSortBy] = useState("Recently Added");
  const [showFilters, setShowFilters] = useState(false);
  const [recentlyViewed, setRecentlyViewed] = useState([]);
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const token = localStorage.getItem("token");

    if (!token) return;
    const fetchRecentlyViewed = async () => {
      try {
        const res = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/recently-viewed`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (!res.ok) throw new Error("Failed to fetch recently viewed");

        const data = await res.json();
        setRecentlyViewed(data?.data || []);
      } catch (error) {
        toast.dismiss();
        toast.error("Error loading recently viewed.");
      } finally {
        setLoading(false);
      }
    };

    fetchRecentlyViewed();
  }, []);

  useEffect(() => {
    const token = localStorage.getItem("token");

    const fetchWishlist = async () => {
      try {
        let wishlist_id = localStorage.getItem("wishlist_id");
        const res = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/wishlist/${wishlist_id}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (!res.ok) throw new Error("Failed to fetch wishlist");

        const data = await res.json();
        setItems(data?.data?.items || []);
      } catch (error) {
        toast.dismiss();
        toast.error("Error loading wishlist.");
      } finally {
        setLoading(false);
      }
    };

    fetchWishlist();
  }, []);

  const { items: wishlistItems } = useAppSelector((state) => state.wishlist);
  const dispatch = useAppDispatch();
  const toggleWishlist = (item) => {
    const wishlistItem = wishlistItems?.find((item) => item.id === item.id);
    if (wishlistItem) {
      dispatch(
        removeFromWishlistApi({
          id: item.id,
        })
      );
      setItems((prevItems) =>
        prevItems.filter((prevItem) => prevItem.id !== item.id)
      );
      toast.dismiss();
      toast.success("Removed from wishlist!");
    } else {
      dispatch(
        addToWishlistApi({
          product_id: item.id,
          name: item.name,
          srcUrl: item.image_with_sizes?.origin[0] || "",
          slug: item.slug,
          price: item.selling_price,
          original_price: item.original_price,
          discount: item.discount,
        })
      );
      setItems((prevItems) => [...prevItems, item]);
      toast.dismiss();
      toast.success("Added to wishlist!");
    }
  };

  const recommendedItems = [];

  const renderStars = (rating, reviews) => (
    <div className="flex items-center gap-1 mb-2">
      {[...Array(5)].map((_, i) => (
        <Star
          key={i}
          className={`w-3 h-3 sm:w-4 sm:h-4 ${
            i < Math.floor(rating)
              ? "fill-yellow-400 text-yellow-400"
              : "text-gray-300"
          }`}
        />
      ))}
      <span className="text-xs sm:text-sm text-gray-500 ml-1">({reviews})</span>
    </div>
  );

  const WishlistCard = ({ item, isListView = false }) => {
    if (isListView) {
      return (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden group hover:shadow-lg transition-shadow duration-300 relative">
          <div className="flex items-start p-3 sm:p-4 gap-3 sm:gap-4">
            <button
              onClick={() => toggleWishlist(item)}
              className="absolute top-3 right-3 z-10 w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center hover:bg-gray-50 active:bg-gray-100"
            >
              <X className="w-4 h-4 text-gray-400" />
            </button>

            {/* Image */}
            <div className="w-20 h-20 sm:w-24 sm:h-24 bg-gray-100 rounded-lg relative overflow-hidden flex-shrink-0">
              <div className="w-full h-full bg-gradient-to-br from-pink-100 to-blue-100 flex items-center justify-center">
                <img
                  src={item.image_with_sizes?.origin[0]}
                  alt={item.name}
                  width={100}
                  height={100}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0 pr-8">
              <div className="flex items-start justify-between mb-2">
                <h3 className="font-medium text-gray-900 text-sm sm:text-base leading-tight pr-2">
                  {item.name}
                </h3>
                {/* <button className="p-1 hover:bg-gray-50 rounded flex-shrink-0">
                  <Share2 className="w-4 h-4 text-gray-400" />
                </button> */}
              </div>

              {renderStars(item.rating, item.reviews)}

              <div className="flex items-center gap-2 sm:gap-3 mb-3">
                <span className="text-lg sm:text-xl font-bold text-gray-900">
                  {item.price_formatted}
                </span>
                {item.original_price_formatted && (
                  <>
                    <span className="text-xs sm:text-sm text-gray-500 line-through">
                      {item.original_price_formatted}
                    </span>
                    {/* <span className="text-xs bg-red-100 text-red-600 px-2 py-1 rounded">
                      {item.discount}
                    </span> */}
                  </>
                )}
              </div>

              <div className="flex items-center justify-between flex-wrap gap-2">
                <div>
                  {item.is_out_of_stock ? (
                    <span className="text-xs sm:text-sm text-red-600 font-medium">
                      Out of Stock
                    </span>
                  ) : (
                    <span className="text-xs sm:text-sm text-green-600 font-medium">
                      In Stock
                    </span>
                  )}
                </div>

                <button
                  onClick={() => {
                    dispatch(addToCart({ ...item, quantity: 1 }));
                    dispatch(
                      addToCartAsync({
                        ...item,
                        product_id: item.id,
                        quantity: 1,
                      })
                    );
                    dispatch(removeFromWishlistApi(item));
                    setItems((prevItems) =>
                      prevItems.filter((prevItem) => prevItem.id !== item.id)
                    );
                    toast.dismiss();
                    toast.success("Item moved to cart!");
                  }}
                  className={`py-2 px-4 sm:px-6 rounded-lg text-xs sm:text-sm font-medium transition-colors min-w-0 ${
                    item.is_out_of_stock
                      ? "bg-primary hover:bg-primary/80 active:bg-primary/60 text-white"
                      : "bg-primary hover:bg-primary/80 active:bg-primary/60 text-white"
                  }`}
                >
                  Move to Cart
                </button>
              </div>
            </div>
          </div>
        </div>
      );
    }

    // Grid view - optimized for mobile
    return (
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden group hover:shadow-lg transition-shadow duration-300 relative">
        <button
          onClick={() => toggleWishlist(item)}
          className="absolute top-2 sm:top-3 right-2 sm:right-3 z-10 w-7 h-7 sm:w-8 sm:h-8 bg-white rounded-full shadow-md flex items-center justify-center hover:bg-gray-50 active:bg-gray-100"
        >
          <X className="w-3 h-3 sm:w-4 sm:h-4 text-gray-400" />
        </button>
        {/* <button className="absolute top-2 sm:top-3 left-2 sm:left-3 z-10 w-7 h-7 sm:w-8 sm:h-8 bg-white rounded-full shadow-md flex items-center justify-center hover:bg-gray-50 active:bg-gray-100">
          <Share2 className="w-3 h-3 sm:w-4 sm:h-4 text-gray-400" />
        </button> */}

        <div className="aspect-square bg-gray-100 relative overflow-hidden">
          <div className="w-full h-full bg-gradient-to-br from-pink-100 to-blue-100 flex items-center justify-center">
            <img
              src={item.image_with_sizes?.origin[0]}
              alt={item.name}
              width={100}
              height={100}
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        <div className="p-3 sm:p-4">
          <h3 className="font-medium text-gray-900 mb-2 text-xs sm:text-sm leading-tight line-clamp-2">
            {item.name}
          </h3>
          {renderStars(item.reviews_avg, item.reviews_count)}

          <div className="flex items-center gap-1 sm:gap-2 mb-3 flex-wrap">
            <span className="text-base sm:text-lg font-bold text-gray-900">
              {item.price_formatted}
            </span>
            {item.original_price_formatted && (
              <>
                <span className="text-xs sm:text-sm text-gray-500 line-through">
                  {item.original_price_formatted}
                </span>
                {/* <span className="text-xs bg-red-100 text-red-600 px-1.5 sm:px-2 py-0.5 sm:py-1 rounded">
                  {item.discount}
                </span> */}
              </>
            )}
          </div>

          <div className="mb-3">
            {item.is_out_of_stock ? (
              <span className="text-xs text-red-500 font-medium">
                Out of Stock
              </span>
            ) : (
              <span className="text-xs text-green-500 font-medium">
                In Stock
              </span>
            )}
          </div>

          <button
            onClick={() => {
              dispatch(addToCart({ ...item, quantity: 1 }));
              dispatch(
                addToCartAsync({ ...item, product_id: item.id, qty: 1 })
              );
              dispatch(removeFromWishlistApi(item));
              setItems((prevItems) =>
                prevItems.filter((prevItem) => prevItem.id !== item.id)
              );
              toast.success("Item moved to cart!");
            }}
            className={`w-full py-2.5 sm:py-2 px-4 rounded-lg text-xs sm:text-sm font-medium transition-colors ${
              item.is_out_of_stock
                ? "bg-primary hover:bg-primary/80 active:bg-primary/60 text-white"
                : "bg-primary hover:bg-primary/80 active:bg-primary/60 text-white"
            }`}
          >
            Move to Cart
          </button>
        </div>
      </div>
    );
  };

  const SmallCard = ({ item, showHeart = false }) => {
    const [isInWishlist, setIsInWishlist] = useState(false);

    const handleWishlistToggle = () => {
      setIsInWishlist((prev) => !prev);
      toggleWishlist(item);
    };
    return (
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden group hover:shadow-md transition-shadow duration-300 relative">
        <Link href={`/shop/product/${item.slug}`}>
          {showHeart && (
            <button
              onClick={(e) => {
                e.preventDefault(); // Prevent navigating to the product page
                handleWishlistToggle();
              }}
              className={`absolute top-1.5 sm:top-2 right-1.5 sm:right-2 z-10 w-5 h-5 sm:w-6 sm:h-6 bg-white rounded-full shadow-sm flex items-center justify-center ${
                isInWishlist ? "text-red-500 bg-red-50" : "text-gray-400"
              }`}
            >
              <Heart className="w-2.5 h-2.5 sm:w-3 sm:h-3" />
            </button>
          )}

          <div className="aspect-square bg-gray-100 relative overflow-hidden">
            <div className="w-full h-full bg-gradient-to-br from-purple-100 to-green-100 flex items-center justify-center">
              <img
                src={item.image}
                alt={item.name}
                width={100}
                height={100}
                className="w-full h-full object-cover"
              />
            </div>
          </div>

          <div className="p-2 sm:p-3">
            <h3 className="font-medium text-gray-900 text-xs mb-1 line-clamp-2">
              {item.name}
            </h3>
            <span className="text-xs sm:text-sm font-bold text-gray-900">
              {item.price}
            </span>
          </div>
        </Link>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-8">
        {/* Mobile Header */}
        <div className="mb-4 sm:mb-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h1 className="text-xl sm:text-2xl font-bold text-gray-900">
                My Wishlist
              </h1>
              <p className="text-gray-600 text-xs sm:text-sm mt-1">
                {items.length} Items Saved
              </p>
            </div>
          </div>

          {/* Mobile Controls */}
          <div className="flex items-center justify-between gap-2 sm:gap-4">
            {/* Sort and Filter for Mobile */}
            <div className="flex items-center gap-2 flex-1">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="sm:hidden flex items-center gap-1 px-3 py-2 border border-gray-300 rounded-md bg-white text-xs font-medium"
              >
                <Filter className="w-3 h-3" />
                Filter
              </button>

              {/* Desktop Sort */}
              <div className="hidden sm:flex items-center gap-2">
                <span className="text-sm text-gray-600">Sort by:</span>
                <div className="relative">
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="pl-3 pr-8 py-2 border border-gray-300 rounded-md bg-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
                  >
                    <option>Recently Added</option>
                    <option>Price: Low to High</option>
                    <option>Price: High to Low</option>
                    <option>Name</option>
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>
              </div>

              {/* Mobile Sort Dropdown */}
              {showFilters && (
                <div className="sm:hidden absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-20 p-3">
                  <div className="mb-3">
                    <label className="block text-xs font-medium text-gray-700 mb-2">
                      Sort by:
                    </label>
                    <select
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option>Recently Added</option>
                      <option>Price: Low to High</option>
                      <option>Price: High to Low</option>
                      <option>Name</option>
                    </select>
                  </div>
                </div>
              )}
            </div>

            {/* View Mode Toggle */}
            <div className="flex items-center bg-white rounded-lg border border-gray-200 p-0.5 sm:p-1">
              <button
                onClick={() => setViewMode("grid")}
                className={`p-1.5 sm:p-2 rounded ${
                  viewMode === "grid"
                    ? "bg-primary text-white"
                    : "text-gray-400 hover:text-gray-600"
                }`}
              >
                <Grid className="w-3 h-3 sm:w-4 sm:h-4" />
              </button>
              <button
                onClick={() => setViewMode("list")}
                className={`p-1.5 sm:p-2 rounded ${
                  viewMode === "list"
                    ? "bg-primary text-white"
                    : "text-gray-400 hover:text-gray-600"
                }`}
              >
                <List className="w-3 h-3 sm:w-4 sm:h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Main Wishlist Items */}
        <div
          className={`mb-8 sm:mb-12 ${
            viewMode === "grid"
              ? "grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6"
              : "space-y-3 sm:space-y-4"
          }`}
        >
          {items.length > 0 ? (
            items.map((item) => (
              <WishlistCard
                key={item.id}
                item={item}
                isListView={viewMode === "list"}
              />
            ))
          ) : (
            <p className="col-span-4 text-center text-gray-600">
              Seems your wishlist is empty :-(
            </p>
          )}
        </div>

        {/* You May Also Like Section */}
        {recommendedItems.length > 0 && (
          <div className="mb-8 sm:mb-12">
            <h2 className="text-lg sm:text-xl font-bold text-gray-900 mb-4 sm:mb-6">
              You May Also Like
            </h2>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-3 sm:gap-4">
              {recommendedItems.map((item) => (
                <SmallCard key={item.id} item={item} showHeart={true} />
              ))}
            </div>
          </div>
        )}

        {/* Recently Viewed Section */}
        <div>
          <h2 className="text-lg sm:text-xl font-bold text-gray-900 mb-4 sm:mb-6">
            Recently Viewed
          </h2>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-3 sm:gap-4">
            {recentlyViewed.length > 0 ? (
              recentlyViewed.map((item) => (
                <SmallCard key={item.id} item={item} />
              ))
            ) : (
              <p className="col-span-4 text-center text-gray-600">
                Seems didn't see any Kawaii
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Overlay for mobile filters */}
      {showFilters && (
        <div
          className="sm:hidden fixed inset-0 bg-black bg-opacity-25 z-10"
          onClick={() => setShowFilters(false)}
        />
      )}
    </div>
  );
};

export default WishlistPage;
