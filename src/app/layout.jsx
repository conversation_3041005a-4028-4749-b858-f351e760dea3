import "@/styles/globals.css";
import { satoshi } from "@/styles/fonts";
import Providers from "./providers";
import ClientLayout from "./ClientLayout";
import { GoogleAnalytics, GoogleTagManager } from "@next/third-parties/google";
import { SearchProvider } from "@/lib/context/SearchContext";
import { Toaster } from "react-hot-toast";
import {
  generateMetaTags,
  generateOrganizationSchema,
  generateWebsiteSchema,
} from "@/lib/utils/seo";

// Enhanced metadata with comprehensive SEO
export const metadata = generateMetaTags({
  title:
    "CromiTopia | Kawaii Shop for Cute Keychains, Stationery & Accessories",
  description:
    "Discover adorable keychains, kawaii stationery, and cute accessories at CromiTopia! Shop the best Japanese kawaii products with worldwide shipping. 🎀✨",
  keywords: [
    "kawaii shop",
    "cute keychains",
    "japanese stationery",
    "kawaii accessories",
    "cute gifts",
    "anime merchandise",
    "japanese products",
    "online kawaii store",
  ],
  type: "website",
});

export const viewport = {
  themeColor: "#000000",
  width: "device-width",
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
};

export default function RootLayout({ children }) {
  // Generate structured data for the organization and website
  const organizationSchema = generateOrganizationSchema();
  const websiteSchema = generateWebsiteSchema();

  return (
    <html lang="en">
      <head>
        {/* Font preloading is handled by Next.js localFont automatically */}

        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(organizationSchema),
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(websiteSchema),
          }}
        />

        {/* Performance and SEO meta tags */}
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />

        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
        <link rel="preconnect" href="https://www.google-analytics.com" />

        {/* DNS prefetch for performance */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//www.google-analytics.com" />
        <link rel="dns-prefetch" href="//connect.facebook.net" />

        {/* Meta Pixel Code */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              !function(f,b,e,v,n,t,s)
              {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
              n.callMethod.apply(n,arguments):n.queue.push(arguments)};
              if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
              n.queue=[];t=b.createElement(e);t.async=!0;
              t.src=v;s=b.getElementsByTagName(e)[0];
              s.parentNode.insertBefore(t,s)}(window, document,'script',
              'https://connect.facebook.net/en_US/fbevents.js');
              fbq('init', '1038575951695219');
              fbq('track', 'PageView');
            `,
          }}
        />
      </head>
      <body className={`${satoshi.className} cursor-default`}>
        {/* Meta Pixel Code - noscript fallback */}
        <noscript>
          <img
            height="1"
            width="1"
            style={{ display: "none" }}
            src="https://www.facebook.com/tr?id=1038575951695219&ev=PageView&noscript=1"
            alt=""
          />
        </noscript>
        <Toaster
          position="top-center"
          toastOptions={{ duration: 2000 }}
          reverseOrder={false}
        />
        <SearchProvider>
          <Providers>
            {/* Conditionally render GoogleAnalytics in production */}
            {process.env.NODE_ENV === "production" && (
              <GoogleAnalytics gaId="G-PF0XYN8KL7" />
            )}
            {process.env.NODE_ENV === "production" && (
              <GoogleTagManager gtmId="GTM-5VCTGSGD" />
            )}
            <ClientLayout>{children}</ClientLayout>
          </Providers>
        </SearchProvider>
      </body>
    </html>
  );
}
