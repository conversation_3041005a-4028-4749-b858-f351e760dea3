import { generateMetaTags, generateCanonicalUrl } from "@/lib/utils/seo";

// Enhanced metadata for shop pages
export const metadata = generateMetaTags({
  title: "Shop Kawaii Products - Cute Accessories & Japanese Stationery",
  description:
    "Browse our collection of kawaii products including cute keychains, Japanese stationery, and adorable accessories. Free worldwide shipping on orders over $50.",
  keywords: [
    "kawaii shop",
    "cute accessories",
    "japanese stationery",
    "kawaii keychains",
    "anime merchandise",
    "cute gifts",
    "japanese products",
    "kawaii store online",
    "cute stationery",
    "kawaii accessories",
  ],
  canonical: generateCanonicalUrl("/shop"),
});

export default function ShopLayout({ children }) {
  return (
    <>
      {/* Structured data for the shop page */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "CollectionPage",
            name: "Shop - CromiTopia",
            description:
              "Browse our collection of kawaii products including cute keychains, Japanese stationery, and adorable accessories.",
            url: process.env.NEXT_PUBLIC_BASE_URL + "/shop",
            mainEntity: {
              "@type": "ItemList",
              name: "Kawaii Products",
              description: "Collection of cute and kawaii products",
            },
          }),
        }}
      />

      {children}
    </>
  );
}
