import { generateMetaTags, generateCanonicalUrl } from "@/lib/utils/seo";

export const metadata = generateMetaTags({
  title: "Shop by Category - Kawaii Products | CromiTopia",
  description: "Browse our kawaii products by category. Find cute accessories, Japanese stationery, plushies, and more organized by category for easy shopping.",
  keywords: [
    "kawaii categories",
    "product categories",
    "cute accessories",
    "japanese products",
    "kawaii shop",
    "online shopping",
    "product catalog",
  ],
  canonical: generateCanonicalUrl("/categories"),
  type: "website",
});

export default function CategoriesLayout({ children }) {
  return (
    <>
      {/* Structured data for categories page */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "CollectionPage",
            name: "Product Categories - CromiTopia",
            description: "Browse kawaii products by category at CromiTopia",
            url: process.env.NEXT_PUBLIC_BASE_URL + "/categories",
            mainEntity: {
              "@type": "ItemList",
              name: "Product Categories",
              description: "All available product categories",
            },
            breadcrumb: {
              "@type": "BreadcrumbList",
              itemListElement: [
                {
                  "@type": "ListItem",
                  position: 1,
                  name: "Home",
                  item: process.env.NEXT_PUBLIC_BASE_URL,
                },
                {
                  "@type": "ListItem",
                  position: 2,
                  name: "Categories",
                  item: process.env.NEXT_PUBLIC_BASE_URL + "/categories",
                },
              ],
            },
          }),
        }}
      />
      {children}
    </>
  );
}
