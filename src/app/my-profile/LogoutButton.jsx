import { clearCart } from "@/lib/features/carts/cartsSlice";
import { clearWishlist } from "@/lib/features/wishlist/wishlistSlice";
import { useAppDispatch } from "@/lib/hooks/redux";
import { useRouter } from "next/navigation";

const LogoutButton = ({ onLogout }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();

  return (
    <button
      onClick={() => {
        localStorage.removeItem("token");
        localStorage.removeItem("cart_id");
        localStorage.removeItem("wishlist_id");
        dispatch(clearCart());
        dispatch(clearWishlist());
        if (onLogout) onLogout(); // Notify the hook about the change
        router.push("/");
      }}
      className="ml-auto bg-red-500 text-white px-4 py-2 rounded shadow"
    >
      Logout
    </button>
  );
};

export default LogoutButton;
