import { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import toast from "react-hot-toast";
import countryData from "@/lib/data/country.json";
import { updateAddress } from "@/lib/features/addresses/addressSlice";

export default function EditAddressModal({ isOpen, onClose, address }) {
  const dispatch = useDispatch();

  // State to hold form data
  const [formData, setFormData] = useState({
    name: "",
    type: "Home",
    phone: "",
    email: "",
    city: "",
    address: "",
    state: "",
    country: "",
    zipCode: "",
    is_default: false,
  });

  // Populate form data when address prop changes
  useEffect(() => {
    if (address) {
      setFormData({
        name: address.name || "",
        type: address.type || "Home",
        phone: address.phone || "",
        email: address.email || "",
        city: address.city || "",
        address: address.address || "",
        state: address.state || "",
        country: address.country || "",
        zipCode: address.zipCode || address.zip_code || "",
        is_default: address.is_default || false,
      });
    }
  }, [address]);

  // Handle input change
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!address?.id) {
      toast.error("Address ID is missing!");
      return;
    }

    try {
      const payload = {
        id: address.id,
        ...formData,
      };

      await dispatch(updateAddress(payload)).unwrap();
      toast.success("Address updated successfully!");
      onClose();
    } catch (error) {
      toast.error("Failed to update address!");
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div className="bg-white w-full max-w-lg md:max-w-2xl rounded-lg shadow-lg p-6 overflow-y-auto max-h-screen">
        <h2 className="text-xl font-semibold mb-4">Edit Address</h2>
        <form
          onSubmit={handleSubmit}
          className="grid grid-cols-1 md:grid-cols-2 gap-4"
        >
          {/* Full Name */}
          <div>
            <label className="block text-sm font-medium mb-1">Full Name</label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Enter your full name"
              className="w-full border rounded-lg px-3 py-2 text-sm"
              required
            />
          </div>

          {/* Address Type */}
          <div>
            <label className="block text-sm font-medium mb-1">
              Address Type
            </label>
            <select
              name="type"
              value={formData.type}
              onChange={handleChange}
              className="w-full border rounded-lg px-3 py-2 text-sm"
            >
              <option value="Home">Home</option>
              <option value="Work">Work</option>
              <option value="Other">Other</option>
            </select>
          </div>

          {/* Mobile Number */}
          <div>
            <label className="block text-sm font-medium mb-1">
              Mobile Number
            </label>
            <input
              type="text"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              placeholder="Enter mobile number"
              className="w-full border rounded-lg px-3 py-2 text-sm"
              required
            />
          </div>

          {/* Email */}
          <div>
            <label className="block text-sm font-medium mb-1">Email</label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Enter email"
              className="w-full border rounded-lg px-3 py-2 text-sm"
            />
          </div>

          {/* Address */}
          <div>
            <label className="block text-sm font-medium mb-1">Address</label>
            <input
              type="text"
              name="address"
              value={formData.address}
              onChange={handleChange}
              placeholder="House/Flat number, Street"
              className="w-full border rounded-lg px-3 py-2 text-sm"
              required
            />
          </div>

          {/* City */}
          <div>
            <label className="block text-sm font-medium mb-1">City</label>
            <input
              type="text"
              name="city"
              value={formData.city}
              onChange={handleChange}
              placeholder="Enter city"
              className="w-full border rounded-lg px-3 py-2 text-sm"
              required
            />
          </div>

          {/* State */}
          <div>
            <label className="block text-sm font-medium mb-1">State</label>
            <input
              type="text"
              name="state"
              value={formData.state}
              onChange={handleChange}
              placeholder="Enter state"
              className="w-full border rounded-lg px-3 py-2 text-sm"
              required
            />
          </div>

          {/* Country */}
          <div>
            <label className="block text-sm font-medium mb-1">Country</label>
            <select
              name="country"
              value={formData.country}
              onChange={handleChange}
              className="w-full border rounded-lg px-3 py-2 text-sm"
              required
            >
              <option value="">Select country</option>
              {countryData.map((country) => (
                <option
                  key={country.country_name}
                  value={country.country_name.toString()}
                >
                  {country.country_name}
                </option>
              ))}
            </select>
          </div>

          {/* ZIP Code */}
          <div>
            <label className="block text-sm font-medium mb-1">ZIP Code</label>
            <input
              type="text"
              name="zipCode"
              value={formData.zipCode}
              onChange={handleChange}
              placeholder="Enter ZIP code"
              className="w-full border rounded-lg px-3 py-2 text-sm"
              required
            />
          </div>

          {/* Set as Default Address */}
          <div className="flex items-center">
            <input
              type="checkbox"
              name="is_default"
              checked={formData.is_default}
              onChange={handleChange}
              id="editDefaultAddress"
              className="h-4 w-4 text-blue-600 border-gray-300 rounded"
            />
            <label
              htmlFor="editDefaultAddress"
              className="ml-2 text-sm text-gray-700"
            >
              Set as default address
            </label>
          </div>

          {/* Buttons */}
          <div className="flex space-x-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-primary text-white rounded-lg"
            >
              Update Address
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
