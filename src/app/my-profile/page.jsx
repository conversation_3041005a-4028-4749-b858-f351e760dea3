"use client";

import {
  deleteAddress,
  fetchAddresses,
} from "@/lib/features/addresses/addressSlice";
import { fetchProfile } from "@/lib/features/profile/profileSlice";
import { useAppSelector } from "@/lib/hooks/redux";
import {
  Building2,
  Calendar,
  HeartIcon,
  Home,
  Lock,
  Mail,
  Package,
  Phone,
  Plus,
  Tag,
  User,
  Edit,
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useDispatch } from "react-redux";
import ConfirmationModal from "../addresses/ConfirmationModal";
import AddAddressModal from "./AddAddressModal";
import ChangePasswordModal from "./ChangePassword";
import EditAddressModal from "./EditAddressModal";
import LogoutButton from "./LogoutButton";
import EditProfileModal from "./EditProfileModal";

export default function ProfilePage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("profile");
  const [isOpen, setIsOpen] = useState(false);
  const [isAddressOpen, setIsAddressOpen] = useState(false);
  const [isEditAddressOpen, setIsEditAddressOpen] = useState(false);
  const [addressToEdit, setAddressToEdit] = useState(null);
  const [isEditProfileOpen, setIsEditProfileOpen] = useState(false);
  const dispatch = useDispatch();

  const { profile } = useAppSelector((state) => state.profile);
  const { addresses } = useAppSelector((state) => state.addresses);

  useEffect(() => {
    dispatch(fetchProfile());
    dispatch(fetchAddresses());
  }, [dispatch]);

  const user = {
    name: profile?.name,
    email: profile?.email,
    phone: profile?.phone,
    avatar: profile?.avatar,
    memberSince: profile?.created_at,
    addresses: addresses,
    recentActivities: [
      { action: "Order #12345 delivered", time: "2 hours ago", icon: Package },
      { action: "Added item to wishlist", time: "Yesterday", icon: HeartIcon },
      { action: "Used coupon KAWAII20", time: "3 days ago", icon: Tag },
    ],
  };

  const quickActions = [
    {
      label: "My Orders",
      icon: Package,
      color: "text-blue-600",
      href: "/orders",
    },
    {
      label: "Wishlist",
      icon: HeartIcon,
      color: "text-pink-600",
      href: "/wishlist",
    },
    {
      label: "My Reviews",
      icon: Tag,
      color: "text-green-600",
      href: "/reviews",
    },
  ];

  const accountSettings = [
    {
      label: "Change Password",
      icon: Lock,
      color: "text-red-600",
      onClick: () => setIsOpen(true),
    },
    // { label: "Notifications", icon: Bell, color: "text-orange-600" },
  ];

  const [isDeleteConfirmationOpen, setDeleteConfirmationOpen] = useState(false);
  const [addressToDelete, setAddressToDelete] = useState(null);
  const confirmDeleteAddress = () => {
    if (addressToDelete) {
      dispatch(deleteAddress(addressToDelete))
        .unwrap()
        .then(() => {
          setDeleteConfirmationOpen(false);
          toast.dismiss();
          toast.success("Address deleted successfully");
          dispatch(fetchAddresses());
        })
        .catch((error) => {
          toast.dismiss();
          toast.error("Error deleting address: " + error);
          setDeleteConfirmationOpen(false);
        });
    }
  };

  const cancelDeleteAddress = () => {
    setDeleteConfirmationOpen(false);
  };

  return (
    <>
      <div className="min-h-screen bg-gray-50">
        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Profile Header */}
          <div className="bg-white rounded-lg shadow-sm md:p-6 p-1 mb-8">
            <div className="flex items-center justify-between space-x-6">
              <div className="flex items-center space-x-6">
                <div className="relative">
                  <img
                    src={user.avatar}
                    alt={`${user.name}'s Avatar`}
                    className="md:w-20 md:h-20 w-24 h-12 rounded-full object-cover"
                  />
                  {/* <button className="absolute bottom-0 right-0 bg-white rounded-full p-1 shadow-md border">
                    <Edit className="w-3 h-3 text-gray-600" />
                  </button> */}
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {user.name}
                  </h1>
                  <p className="text-gray-500">
                    Member since {user.memberSince}
                  </p>
                </div>
              </div>
              <LogoutButton
                onLogout={() => window.dispatchEvent(new Event("storage"))}
              />
            </div>
          </div>

          {/* Main Grid Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - Personal Information */}
            <div className="lg:col-span-2 space-y-8">
              {/* Personal Information */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-lg font-semibold text-gray-900 w-full">
                    Personal Information
                  </h2>
                  <button
                    onClick={() => setIsEditProfileOpen(true)}
                    className="w-full justify-end text-teal-600 hover:text-teal-700 flex items-center gap-1 text-sm font-medium"
                  >
                    <Edit className="w-4 h-4" />
                    Edit Profile
                  </button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-500">
                      Full Name
                    </label>
                    <div className="flex items-center space-x-2">
                      <User className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-900">{user.name}</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-500">
                      Email
                    </label>
                    <div className="flex items-center space-x-2">
                      <Mail className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-900">{user.email}</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-500">
                      Phone
                    </label>
                    <div className="flex items-center space-x-2">
                      <Phone className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-900">
                        {user.phone ?? "N/A"}
                      </span>
                    </div>
                  </div>
                  {user.birthday && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-500">
                        Birthday
                      </label>
                      <div className="flex items-center space-x-2">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-900">{user.birthday}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Addresses */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-lg font-semibold text-gray-900">
                    My Addresses
                  </h2>
                  <button
                    onClick={() => setIsAddressOpen(true)}
                    className="bg-primary hover:bg-primary/80 text-white md:px-4 px-2 py-2 rounded-lg text-sm font-medium flex items-center space-x-2"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Add New Address</span>
                  </button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {user.addresses.map((address, index) => (
                    <div
                      key={index}
                      className="border border-gray-200 rounded-lg p-4"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-3">
                          <div className="mt-1">
                            {address.type === "Home" ? (
                              <Home className="w-5 h-5 text-gray-600" />
                            ) : (
                              <Building2 className="w-5 h-5 text-gray-600" />
                            )}
                          </div>
                          <div>
                            <div className="flex items-center space-x-2">
                              <h3 className="font-medium text-gray-900">
                                {address.type}
                              </h3>
                              {address.is_default ? (
                                <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                                  Default
                                </span>
                              ) : (
                                ""
                              )}
                            </div>
                            <p className="text-sm text-gray-600 mt-1">
                              {address.full_address}
                            </p>
                            <div className="flex items-center space-x-4 mt-3">
                              <button
                                onClick={() => {
                                  setAddressToEdit(address);
                                  setIsEditAddressOpen(true);
                                }}
                                className="text-teal-600 hover:text-teal-700 text-sm font-medium"
                              >
                                Edit
                              </button>
                              <button
                                onClick={() => {
                                  setAddressToDelete(address.id);
                                  setDeleteConfirmationOpen(true);
                                }}
                                className="text-red-600 hover:text-red-700 text-sm font-medium"
                              >
                                Delete
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Column - Quick Actions, Account Settings, Recent Activity */}
            <div>
              {/* Quick Actions */}
              <div className="bg-blue-50 rounded-lg p-6 mb-5">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  Quick Actions
                </h2>
                <div className="space-y-3">
                  {quickActions.map((action, index) => {
                    const IconComponent = action.icon;
                    return (
                      <Link
                        href={action.href}
                        key={index}
                        className="w-full flex items-center space-x-3 p-3 rounded-lg bg-white transition-colors duration-200"
                      >
                        <IconComponent className={`w-5 h-5 ${action.color}`} />
                        <span className="text-gray-900 font-medium">
                          {action.label}
                        </span>
                      </Link>
                    );
                  })}
                </div>
              </div>

              {/* Account Settings */}
              <div className="bg-red-50 rounded-lg p-6 mb-5">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  Account Settings
                </h2>
                <div className="space-y-3">
                  {accountSettings.map((setting, index) => {
                    const IconComponent = setting.icon;
                    return (
                      <button
                        onClick={setting.onClick}
                        key={index}
                        className="w-full flex items-center space-x-3 p-3 rounded-lg bg-white transition-colors duration-200"
                      >
                        <IconComponent className={`w-5 h-5 ${setting.color}`} />
                        <span className="text-gray-900 font-medium">
                          {setting.label}
                        </span>
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Recent Activity */}
              {/* <div className="bg-white rounded-lg shadow-sm p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  Recent Activity
                </h2>
                <div className="space-y-4">
                  {user.recentActivities.map((activity, index) => {
                    const IconComponent = activity.icon;
                    return (
                      <div key={index} className="flex items-start space-x-3">
                        <div className="mt-1">
                          <IconComponent className="w-4 h-4 text-gray-400" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm text-gray-900">
                            {activity.action}
                          </p>
                          <p className="text-xs text-gray-500 mt-1">
                            {activity.time}
                          </p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div> */}
            </div>
          </div>

          {/* Help Section */}
          <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 text-sm">?</span>
                </div>
                <span className="text-gray-900 font-medium">Need Help?</span>
              </div>
              <a
                href="mailto:<EMAIL>"
                className="text-teal-600 hover:text-teal-700 font-medium"
              >
                Contact Support
              </a>
            </div>
          </div>
        </div>
      </div>
      <ChangePasswordModal isOpen={isOpen} onClose={() => setIsOpen(false)} />
      <AddAddressModal
        isOpen={isAddressOpen}
        onClose={() => setIsAddressOpen(false)}
      />
      <ConfirmationModal
        isOpen={isDeleteConfirmationOpen}
        onClose={cancelDeleteAddress}
        onConfirm={confirmDeleteAddress}
        message="Are you sure you want to delete this address?"
      />

      <EditAddressModal
        isOpen={isEditAddressOpen}
        onClose={() => setIsEditAddressOpen(false)}
        address={addressToEdit}
      />
      <EditProfileModal
        isOpen={isEditProfileOpen}
        onClose={() => setIsEditProfileOpen(false)}
        currentName={user.name}
      />
    </>
  );
}
