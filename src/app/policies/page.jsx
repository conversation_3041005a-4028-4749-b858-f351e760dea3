"use client";

import React from "react";
import Link from "next/link";

export default function PoliciesPage() {
  return (
    <main className="pb-20">
      <div className="max-w-4xl mx-auto px-4 xl:px-0 py-8">
        <hr className="h-[1px] border-t-black/10 mb-5 sm:mb-6" />

        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-8">
          <Link href="/" className="hover:text-primary">
            Home
          </Link>
          <span>/</span>
          <span className="text-gray-900 font-medium">Policies</span>
        </nav>

        {/* Page Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Store Policies
          </h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Important information about shopping with CromiTopia. Please read
            our policies carefully before making a purchase.
          </p>
        </div>

        {/* Policies Navigation */}
        <div className="grid md:grid-cols-3 gap-6 mb-12">
          <a
            href="#shipping"
            className="p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <h3 className="font-semibold text-gray-900 mb-2">
              🚚 Shipping Policy
            </h3>
            <p className="text-sm text-gray-600">
              Delivery times and shipping information
            </p>
          </a>
          <a
            href="#return"
            className="p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <h3 className="font-semibold text-gray-900 mb-2">
              ↩️ Return Policy
            </h3>
            <p className="text-sm text-gray-600">
              Our no-return policy explained
            </p>
          </a>
          <a
            href="#payment"
            className="p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <h3 className="font-semibold text-gray-900 mb-2">
              💳 Payment Policy
            </h3>
            <p className="text-sm text-gray-600">
              Accepted payment methods and security
            </p>
          </a>
        </div>

        <div className="space-y-12">
          {/* Shipping Policy */}
          <section id="shipping" className="scroll-mt-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 border-b border-gray-200 pb-2">
              🚚 Shipping Policy
            </h2>

            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Processing Time
                </h3>
                <p className="text-gray-600 mb-2">
                  Orders are typically processed within 1-2 business days.
                  During peak seasons or sales events, processing may take up to
                  3-5 business days.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Shipping Information
                </h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-gray-900">
                      Standard Delivery
                    </span>
                    <span className="text-gray-600">5-7 business days</span>
                  </div>
                  <div className="text-sm text-gray-600 mb-3">
                    We offer reliable standard delivery across India.
                  </div>
                  <div className="bg-green-100 border border-green-200 rounded-lg p-3">
                    <p className="text-green-800 font-medium">
                      🎉 Free delivery on orders over ₹499
                    </p>
                    <p className="text-green-700 text-sm mt-1">
                      Orders below ₹499 will have applicable delivery charges
                    </p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  International Shipping
                </h3>
                <p className="text-gray-600">
                  We currently ship worldwide. International shipping times vary
                  by destination (7-21 business days). Customers are responsible
                  for any customs duties, taxes, or fees imposed by their
                  country.
                </p>
              </div>
            </div>
          </section>

          {/* Return Policy */}
          <section id="return" className="scroll-mt-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 border-b border-gray-200 pb-2">
              ↩️ Return Policy
            </h2>

            <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <svg
                    className="h-6 w-6 text-red-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                    />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-semibold text-red-800 mb-2">
                    No Returns Policy
                  </h3>
                  <p className="text-red-700">
                    <strong>All sales are final.</strong> We do not accept
                    returns, exchanges, or provide refunds for any orders once
                    they have been placed and processed.
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Why No Returns?
                </h3>
                <ul className="list-disc list-inside text-gray-600 space-y-2">
                  <li>
                    To maintain the highest quality and hygiene standards for
                    all customers
                  </li>
                  <li>
                    To offer competitive pricing by reducing operational costs
                  </li>
                  <li>
                    To ensure product authenticity and prevent counterfeit
                    returns
                  </li>
                  <li>
                    To streamline our operations and focus on product quality
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Before You Order
                </h3>
                <p className="text-gray-600 mb-2">
                  Please carefully review your order before completing your
                  purchase:
                </p>
                <ul className="list-disc list-inside text-gray-600 space-y-1">
                  <li>Check product descriptions, sizes, and specifications</li>
                  <li>Review product images and details</li>
                  <li>Verify shipping address and contact information</li>
                  <li>Ensure you understand our no-return policy</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Damaged or Defective Items
                </h3>
                <p className="text-gray-600">
                  If you receive a damaged or defective item, please contact us
                  within 48 hours of delivery with photos of the issue. We will
                  review each case individually and may offer a replacement or
                  store credit at our discretion.
                </p>
              </div>
            </div>
          </section>

          {/* Payment Policy */}
          <section id="payment" className="scroll-mt-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 border-b border-gray-200 pb-2">
              💳 Payment Policy
            </h2>

            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Accepted Payment Methods
                </h3>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <span className="text-2xl">💳</span>
                    <div>
                      <p className="font-medium text-gray-900">
                        Razorpay Payment Gateway
                      </p>
                      <p className="text-sm text-gray-600">
                        Credit/Debit Cards, UPI, Net Banking, Wallets
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <span className="text-2xl">🔒</span>
                    <div>
                      <p className="font-medium text-gray-900">
                        Secure Payment
                      </p>
                      <p className="text-sm text-gray-600">
                        PCI DSS compliant & SSL encrypted
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Payment Security
                </h3>
                <p className="text-gray-600 mb-2">
                  Your payment information is secure with us:
                </p>
                <ul className="list-disc list-inside text-gray-600 space-y-1">
                  <li>
                    All transactions are processed through Razorpay's secure
                    gateway
                  </li>
                  <li>
                    We do not store your payment information on our servers
                  </li>
                  <li>
                    Razorpay is PCI DSS Level 1 compliant with bank-grade
                    security
                  </li>
                  <li>
                    All transactions are encrypted using 256-bit SSL technology
                  </li>
                  <li>
                    Your personal and financial data is protected at all times
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Billing Information
                </h3>
                <p className="text-gray-600">
                  Please ensure your billing information matches your payment
                  method exactly. Orders with mismatched billing information may
                  be delayed or cancelled for security reasons.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Order Confirmation
                </h3>
                <p className="text-gray-600">
                  You will receive an email confirmation once your payment is
                  processed successfully. This email will include your order
                  details and tracking information when available.
                </p>
              </div>
            </div>
          </section>

          {/* Contact Information */}
          <section className="bg-gray-50 rounded-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Questions About Our Policies?
            </h2>
            <p className="text-gray-600 mb-4">
              If you have any questions about our policies or need assistance
              with your order, please don't hesitate to contact us.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                href="/contact-us"
                className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors text-center"
              >
                Contact Support
              </Link>
              <a
                href="mailto:<EMAIL>"
                className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors text-center"
              >
                Email Us
              </a>
            </div>
          </section>
        </div>
      </div>
    </main>
  );
}
