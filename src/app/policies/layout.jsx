import { generateMetaTags, generateCanonicalUrl } from "@/lib/utils/seo";

export const metadata = generateMetaTags({
  title: "Store Policies - Shipping, Returns & Payment | CromiTopia",
  description: "Read CromiTopia's store policies including our no-return policy, shipping information, and payment terms. All sales are final - please review before purchasing.",
  keywords: [
    "store policies",
    "no return policy",
    "shipping policy",
    "payment policy",
    "ecommerce policies",
    "terms of service",
    "kawaii shop policies",
    "final sale",
    "shipping information",
    "payment methods",
  ],
  canonical: generateCanonicalUrl("/policies"),
  type: "website",
});

export default function PoliciesLayout({ children }) {
  return (
    <>
      {/* Structured data for policies page */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            name: "Store Policies - CromiTopia",
            description: "CromiTopia store policies including shipping, returns, and payment information",
            url: process.env.NEXT_PUBLIC_BASE_URL + "/policies",
            mainEntity: {
              "@type": "Organization",
              name: "CromiTopia",
              url: process.env.NEXT_PUBLIC_BASE_URL,
              hasPolicy: [
                {
                  "@type": "ReturnPolicy",
                  name: "No Return Policy",
                  description: "All sales are final. No returns, exchanges, or refunds accepted.",
                  returnPolicyCategory: "NoReturnsPolicy"
                },
                {
                  "@type": "ShippingRateSettings",
                  name: "Shipping Policy",
                  description: "Standard, express, and overnight shipping options available worldwide"
                }
              ]
            },
            breadcrumb: {
              "@type": "BreadcrumbList",
              itemListElement: [
                {
                  "@type": "ListItem",
                  position: 1,
                  name: "Home",
                  item: process.env.NEXT_PUBLIC_BASE_URL,
                },
                {
                  "@type": "ListItem",
                  position: 2,
                  name: "Policies",
                  item: process.env.NEXT_PUBLIC_BASE_URL + "/policies",
                },
              ],
            },
          }),
        }}
      />
      {children}
    </>
  );
}
