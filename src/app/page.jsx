"use client";

import ProductListSec from "@/components/common/ProductListSec";
import TopSellingSec from "@/components/common/TopSellingSec";
import { useEffect, useState } from "react";
import PlushToysSlider from "../components/carousel";
import apiServiceWrapper from "@/lib/services/apiService";
import apiRoutes from "@/lib/constant";
import { useAppDispatch } from "@/lib/hooks/redux";

export default function Home() {
  const [products, setProducts] = useState([]);
  const [popularProducts, setPopularProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const dispatch = useAppDispatch();

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const response = await Promise.all([
          apiServiceWrapper.get(apiRoutes.PRODUCTS + "?collection=5"),
          apiServiceWrapper.get(apiRoutes.PRODUCTS + "?collection=6"),
        ]);
        const results = await Promise.all(response.map((res) => res));

        const newArrivalResult = results[0];
        const popularProductsResult = results[1];

        if (
          newArrivalResult.error === false &&
          popularProductsResult.error === false
        ) {
          // const decryptedNewArrival = decryptData(newArrivalResult.data);
          const newArrivalList = newArrivalResult.data;
          setProducts(newArrivalList ?? []);

          const popularProductList = popularProductsResult.data;
          setPopularProducts(popularProductList ?? []);
        } else {
          throw new Error(
            newArrivalResult.message || "Failed to fetch products"
          );
        }
      } catch (error) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [dispatch]);

  return (
    <>
      <PlushToysSlider />

      <main className="my-[50px] sm:my-[72px]">
        <ProductListSec
          title="Shop by Category"
          data={[]}
          viewAllLink="/categories"
        />
        <div className="max-w-frame mx-auto px-4 xl:px-0">
          <hr className="h-[1px] border-t-black/10 my-10 sm:my-16" />
        </div>
        {products?.length > 0 && (
          <div className="mb-[50px] sm:mb-20">
            <TopSellingSec
              title="New Arrival"
              viewAllLink="/shop?is_new_arrival=true"
              data={products}
            />
          </div>
        )}
        <div className="max-w-frame mx-auto px-4 xl:px-0">
          <hr className="h-[1px] border-t-black/10 my-10 sm:my-16" />
        </div>
        <div className="mb-[50px] sm:mb-20">
          <TopSellingSec
            title="Most Popular"
            data={popularProducts}
            viewAllLink="/shop?filter=most-selling"
          />
        </div>
        {/* <Brands /> */}
        {/* <div className="relative top-[100px] z-10">
          <div className="px-4">
            <NewsLetterSection />
          </div>
        </div> */}
        {/* <div className="mb-[50px] sm:mb-20">
          <DressStyle />
        </div> */}
        {/* <Reviews data={[]} /> */}
      </main>
    </>
  );
}
