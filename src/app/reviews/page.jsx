"use client";

import React, { useEffect, useState } from "react";
import {
  Star,
  Calendar,
  Package,
  MapPin,
  Edit3,
  ChevronDown,
  Search,
  X,
} from "lucide-react";
import toast from "react-hot-toast";

const ReviewsPage = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("recent");
  const [expandedReview, setExpandedReview] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [selectedReview, setSelectedReview] = useState(null);
  const [modalRating, setModalRating] = useState(0);
  const [modalComment, setModalComment] = useState("");
  const [hoverRating, setHoverRating] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [reviews, setReviews] = useState([]);
  const stats = {
    total: reviews.length,
    pending: reviews.filter((r) => r.status === "pending").length,
    averageRating:
      reviews.reduce((acc, r) => acc + r.rating, 0) / reviews.length || 0,
  };

  const filteredReviews = reviews.filter((review) => {
    const matchesTab = activeTab === "all" || review.status === activeTab;
    const matchesSearch =
      review.product_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      review.code.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesTab && matchesSearch;
  });

  useEffect(() => {
    const token = localStorage?.getItem("token");

    const fetchOrders = async () => {
      try {
        const res = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/reviews-products`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (!res.ok) throw new Error("Failed to fetch orders");

        const data = await res.json();
        setReviews(data?.data || []);
      } catch (error) {
        console.error("Error loading reviews:", error);
      }
    };

    fetchOrders();
  }, []);

  const renderStars = (rating, size = "w-4 h-4") => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${size} ${
              star <= rating
                ? "fill-yellow-400 text-yellow-400"
                : "text-gray-300"
            }`}
          />
        ))}
      </div>
    );
  };

  const renderInteractiveStars = (currentRating, onRate, onHover, onLeave) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => onRate(star)}
            onMouseEnter={() => onHover(star)}
            onMouseLeave={onLeave}
            className="focus:outline-none transition-colors duration-150"
          >
            <Star
              className={`w-8 h-8 ${
                star <= (hoverRating || currentRating)
                  ? "fill-yellow-400 text-yellow-400"
                  : "text-gray-300 hover:text-yellow-300"
              }`}
            />
          </button>
        ))}
      </div>
    );
  };

  const toggleExpanded = (reviewId) => {
    setExpandedReview(expandedReview === reviewId ? null : reviewId);
  };

  const handleEditClick = (review) => {
    setSelectedReview(review);
    setModalRating(review.rating || 0);
    setModalComment(review.reviewText || "");
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedReview(null);
    setModalRating(0);
    setModalComment("");
    setHoverRating(0);
  };

  const handleSubmitReview = async () => {
    if (!selectedReview || modalRating === 0 || !modalComment.trim()) {
      alert("Please provide both rating and comment");
      return;
    }

    setIsSubmitting(true);

    try {
      const token = localStorage?.getItem("token");
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/reviews`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            product_id: selectedReview.id,
            star: modalRating,
            comment: modalComment.trim(),
          }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to submit review");
      }

      const result = await response.json();
      toast.success("Thanks for your review!");

      // Update the local reviews state
      setReviews((prevReviews) =>
        prevReviews.map((review) =>
          review.id === selectedReview.id
            ? {
                ...review,
                rating: modalRating,
                reviewText: modalComment.trim(),
                status: "completed",
              }
            : review
        )
      );

      closeModal();
    } catch (error) {
      console.error("Error submitting review:", error);
      toast.error("Failed to submit review. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">My Reviews</h1>
          <p className="text-gray-600">Manage and view your product reviews</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg hover:shadow-xl transition-all duration-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Total Reviews
                </p>
                <p className="text-3xl font-bold text-gray-900">
                  {stats.total}
                </p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                <Package className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg hover:shadow-xl transition-all duration-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-3xl font-bold text-yellow-600">
                  {stats.pending}
                </p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-600 rounded-xl flex items-center justify-center">
                <Calendar className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg hover:shadow-xl transition-all duration-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Average Rating
                </p>
                <p className="text-3xl font-bold text-purple-600">
                  {stats.averageRating.toFixed(1)}
                </p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center">
                <Star className="w-6 h-6 text-white fill-current" />
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg mb-8">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
            {/* Tabs */}
            <div className="flex space-x-1 bg-gray-100 rounded-xl p-1">
              {[
                { key: "all", label: "All Reviews", count: stats.total },
                { key: "pending", label: "Pending", count: stats.pending },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                    activeTab === tab.key
                      ? "bg-white text-purple-600 shadow-sm"
                      : "text-gray-600 hover:text-purple-600"
                  }`}
                >
                  {tab.label} ({tab.count})
                </button>
              ))}
            </div>

            {/* Search and Sort */}
            <div className="flex space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search reviews..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 bg-white rounded-lg border border-gray-200 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-4 py-2 bg-white rounded-lg border border-gray-200 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              >
                <option value="recent">Recent First</option>
                <option value="oldest">Oldest First</option>
                <option value="rating">Highest Rating</option>
                <option value="lowest">Lowest Rating</option>
              </select>
            </div>
          </div>
        </div>

        {/* Reviews List */}
        <div className="space-y-6">
          {filteredReviews.map((review) => (
            <div
              key={review.id}
              className="bg-white/70 my-4 backdrop-blur-sm rounded-2xl border border-white/20 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
            >
              <div className="p-6">
                <div className="flex items-start space-x-4">
                  {/* Product Image */}
                  <div className="w-20 h-20 bg-gradient-to-br from-purple-100 to-blue-100 rounded-xl flex-shrink-0 flex items-center justify-center">
                    <Package className="w-8 h-8 text-purple-600" />
                  </div>

                  {/* Review Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">
                          {review.product_name}
                        </h3>
                        <p className="text-sm text-gray-500 mb-2">
                          Order ID: {review.code}
                        </p>
                        <div className="flex items-center space-x-4 mb-3">
                          {renderStars(review.rating)}
                          <span className="text-sm text-gray-500 flex items-center">
                            <Calendar className="w-4 h-4 mr-1" />
                            {review.delivered_date}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span
                          className={`px-3 py-1 rounded-full text-xs font-medium ${
                            review.status === "completed"
                              ? "bg-green-100 text-green-800"
                              : "bg-yellow-100 text-yellow-800"
                          }`}
                        >
                          {review.status === "completed"
                            ? "Completed"
                            : "Pending"}
                        </span>
                        <button
                          onClick={() => handleEditClick(review)}
                          className="p-2 text-gray-400 hover:text-purple-600 transition-colors"
                        >
                          <Edit3 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    <p className="text-gray-700 mb-4">{review.reviewText}</p>

                    <button
                      onClick={() => toggleExpanded(review.id)}
                      className="flex items-center text-sm text-purple-600 hover:text-purple-700 transition-colors"
                    >
                      View Order Details
                      <ChevronDown
                        className={`w-4 h-4 ml-1 transition-transform duration-200 ${
                          expandedReview === review.id ? "rotate-180" : ""
                        }`}
                      />
                    </button>
                  </div>
                </div>

                {/* Expanded Order Details */}
                {expandedReview === review.id && (
                  <div className="mt-6 pt-6 border-t border-gray-200 animate-in slide-in-from-top-2 duration-200">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                      <div>
                        <p className="text-sm font-medium text-gray-600 mb-1">
                          Order Date
                        </p>
                        <p className="text-sm text-gray-900">
                          {review.order_date}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600 mb-1">
                          Delivery Date
                        </p>
                        <p className="text-sm text-gray-900">
                          {review.delivered_date}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600 mb-1">
                          Price
                        </p>
                        <p className="text-sm text-gray-900">${review.price}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600 mb-1">
                          Quantity
                        </p>
                        <p className="text-sm text-gray-900">{review.qty}</p>
                      </div>
                    </div>
                    <div className="mt-4">
                      <p className="text-sm font-medium text-gray-600 mb-1 flex items-center">
                        <MapPin className="w-4 h-4 mr-1" />
                        Shipping Address
                      </p>
                      <p className="text-sm text-gray-900">
                        {review.shipping_address}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {filteredReviews.length === 0 && (
          <div className="text-center py-12">
            <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No reviews found
            </h3>
            <p className="text-gray-500">
              Try adjusting your search or filter criteria.
            </p>
          </div>
        )}
      </div>

      {/* Review Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Modal Header */}
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-gray-900">
                  Write Review
                </h2>
                <button
                  onClick={closeModal}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5 text-gray-500" />
                </button>
              </div>

              {/* Product Info */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="font-medium text-gray-900 mb-1">
                  {selectedReview?.product_name}
                </h3>
                <p className="text-sm text-gray-500">
                  Order ID: {selectedReview?.code}
                </p>
              </div>

              {/* Rating */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Your Rating *
                </label>
                {renderInteractiveStars(
                  modalRating,
                  setModalRating,
                  setHoverRating,
                  () => setHoverRating(0)
                )}
                <p className="text-sm text-gray-500 mt-2">
                  {modalRating === 0
                    ? "Please select a rating"
                    : modalRating === 1
                      ? "Poor"
                      : modalRating === 2
                        ? "Fair"
                        : modalRating === 3
                          ? "Good"
                          : modalRating === 4
                            ? "Very Good"
                            : "Excellent"}
                </p>
              </div>

              {/* Comment */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Your Review *
                </label>
                <textarea
                  value={modalComment}
                  onChange={(e) => setModalComment(e.target.value)}
                  placeholder="Share your experience with this product..."
                  rows={4}
                  maxLength={5000}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                />
                <p className="text-sm text-gray-500 mt-1">
                  {modalComment.length}/5000 characters
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-3">
                <button
                  onClick={closeModal}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  onClick={handleSubmitReview}
                  disabled={
                    modalRating === 0 || !modalComment.trim() || isSubmitting
                  }
                  className="flex-1 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isSubmitting ? "Submitting..." : "Submit Review"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReviewsPage;
