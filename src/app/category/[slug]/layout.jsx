import { generateMetaTags, generateCanonicalUrl } from "@/lib/utils/seo";
import apiServiceWrapper from "@/lib/services/apiService";
import apiRoutes from "@/lib/constant";

// Generate metadata for category pages
export async function generateMetadata({ params }) {
  try {
    // Fetch categories to find the one with matching slug
    const categoriesResponse = await apiServiceWrapper.get(apiRoutes.CATEGORY);
    
    if (!categoriesResponse.error && categoriesResponse.data) {
      const category = categoriesResponse.data.find(cat => cat.slug === params.slug);
      
      if (category) {
        return generateMetaTags({
          title: `${category.name} - Shop Kawaii Products | CromiTopia`,
          description: category.description || 
            `Shop ${category.name} products at CromiTopia. Discover adorable kawaii items, cute accessories, and Japanese stationery with worldwide shipping.`,
          keywords: [
            category.name.toLowerCase(),
            `kawaii ${category.name.toLowerCase()}`,
            `cute ${category.name.toLowerCase()}`,
            "japanese products",
            "kawaii shop",
            "cute accessories",
            "online shopping",
          ],
          canonical: generateCanonicalUrl(`/category/${category.slug}`),
          type: "website",
        });
      }
    }
    
    // Fallback metadata if category not found
    return {
      title: "Category Not Found | CromiTopia",
      description: "The category you're looking for could not be found.",
      robots: "noindex,nofollow",
    };
  } catch (error) {
    console.error("Error generating category metadata:", error);
    return {
      title: "Category | CromiTopia",
      description: "Browse our kawaii product categories at CromiTopia.",
    };
  }
}

export default function CategoryLayout({ children }) {
  return (
    <>
      {/* Structured data for category page */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "CollectionPage",
            name: "Category - CromiTopia",
            description: "Browse products by category at CromiTopia kawaii shop",
            url: process.env.NEXT_PUBLIC_BASE_URL + "/category",
            mainEntity: {
              "@type": "ItemList",
              name: "Category Products",
              description: "Products in this category",
            },
          }),
        }}
      />
      {children}
    </>
  );
}
