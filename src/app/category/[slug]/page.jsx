"use client";

import { useState, useEffect } from "react";
import {
  useParams,
  useSearchParams,
  useRouter,
  usePathname,
} from "next/navigation";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import ProductCard from "@/components/common/ProductCard";
import SpinnerbLoader from "@/components/ui/SpinnerbLoader";
import apiServiceWrapper from "@/lib/services/apiService";
import apiRoutes from "@/lib/constant";

export default function CategoryPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const categorySlug = params.slug;
  const urlPage = parseInt(searchParams?.get("page")) || 1;
  const urlSort = searchParams?.get("sort") || "low-price";

  const [products, setProducts] = useState([]);
  const [category, setCategory] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(urlPage);
  const [totalRecord, setTotalRecord] = useState(0);
  const [sort, setSort] = useState(urlSort);

  const itemsPerPage = 12;

  // Scroll to top when component mounts or category changes
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, [categorySlug]);

  // Function to update URL parameters
  const updateURL = (newParams) => {
    const current = new URLSearchParams(Array.from(searchParams.entries()));

    Object.entries(newParams).forEach(([key, value]) => {
      if (value === null || value === undefined || value === "") {
        current.delete(key);
      } else if (key === "page" && value === 1) {
        current.delete(key);
      } else {
        current.set(key, value);
      }
    });

    const search = current.toString();
    const query = search ? `?${search}` : "";
    router.push(`${pathname}${query}`, { scroll: false });
  };

  // Function to generate clean URLs for pagination
  const generatePageURL = (pageNum) => {
    const current = new URLSearchParams(Array.from(searchParams.entries()));
    if (pageNum === 1) {
      current.delete("page");
    } else {
      current.set("page", pageNum.toString());
    }
    const search = current.toString();
    return search ? `${pathname}?${search}` : pathname;
  };

  // Sync URL parameters with state
  useEffect(() => {
    const urlPage = parseInt(searchParams?.get("page")) || 1;
    const urlSort = searchParams?.get("sort") || "low-price";

    setCurrentPage(urlPage);
    setSort(urlSort);
  }, [searchParams]);

  // Fetch category details and products
  useEffect(() => {
    const fetchCategoryData = async () => {
      try {
        setLoading(true);
        setError(null);

        // First, get all categories to find the one with matching slug
        const categoriesResponse = await apiServiceWrapper.get(
          apiRoutes.CATEGORY
        );

        if (!categoriesResponse.error && categoriesResponse.data) {
          const foundCategory = categoriesResponse.data.find(
            (cat) => cat.slug === categorySlug
          );

          if (!foundCategory) {
            setError("Category not found");
            setLoading(false);
            return;
          }

          setCategory(foundCategory);

          // Now fetch products for this category
          let url = `${apiRoutes.PRODUCTS}?page=${currentPage}&per_page=${itemsPerPage}&sort-by=${sort === "low-price" ? "price_asc" : "price_desc"}`;
          url += `&categories=${foundCategory.id}`;

          const productsResponse = await apiServiceWrapper.get(url);

          if (!productsResponse.error) {
            setProducts(productsResponse.data || []);
            setTotalRecord(productsResponse.meta?.total || 0);
          } else {
            throw new Error(
              productsResponse.message || "Failed to fetch products"
            );
          }
        } else {
          throw new Error(
            categoriesResponse.message || "Failed to fetch categories"
          );
        }
      } catch (error) {
        console.error("Category page error:", error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    if (categorySlug) {
      fetchCategoryData();
    }
  }, [categorySlug, currentPage, sort]);

  const firstProductIndex =
    totalRecord > 0 && currentPage >= 1
      ? (currentPage - 1) * itemsPerPage + 1
      : 0;
  const lastProductIndex = Math.min(currentPage * itemsPerPage, totalRecord);
  const totalPages = Math.ceil(totalRecord / itemsPerPage);

  if (loading) {
    return (
      <main className="pb-20">
        <div className="max-w-frame mx-auto px-4 xl:px-0">
          <div className="flex items-center justify-center min-h-[400px]">
            <SpinnerbLoader className="w-10 border-2 border-gray-300 border-r-gray-600" />
          </div>
        </div>
      </main>
    );
  }

  if (error) {
    return (
      <main className="pb-20">
        <div className="max-w-frame mx-auto px-4 xl:px-0">
          <div className="text-center py-20">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Category Not Found
            </h1>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={() => router.push("/shop")}
              className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors"
            >
              Browse All Products
            </button>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="pb-20">
      <div className="max-w-frame mx-auto px-4 xl:px-0">
        <hr className="h-[1px] border-t-black/10 mb-5 sm:mb-6" />

        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-6">
          <button
            onClick={() => router.push("/")}
            className="hover:text-primary"
          >
            Home
          </button>
          <span>/</span>
          <button
            onClick={() => router.push("/shop")}
            className="hover:text-primary"
          >
            Shop
          </button>
          <span>/</span>
          <span className="text-gray-900 font-medium">{category?.name}</span>
        </nav>

        {/* Category Header */}
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {category?.name}
          </h1>
          {category?.description && (
            <p className="text-gray-600 max-w-2xl">{category.description}</p>
          )}
        </div>

        {/* Products Header */}
        <div className="flex flex-col lg:flex-row lg:justify-between mb-6">
          <span className="text-sm md:text-base text-black/60 mb-4 lg:mb-0">
            Showing {firstProductIndex} to {lastProductIndex} of {totalRecord}{" "}
            products
          </span>

          <div className="flex items-center">
            Sort by:{" "}
            <Select
              value={sort}
              onValueChange={(value) => {
                setSort(value);
                updateURL({ sort: value, page: 1 });
                setCurrentPage(1);
                window.scrollTo({ top: 0, behavior: "smooth" });
              }}
            >
              <SelectTrigger className="focus:ring-0 font-medium text-sm px-1.5 sm:text-base w-fit text-black bg-transparent shadow-none border-none">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low-price">Low Price</SelectItem>
                <SelectItem value="high-price">High Price</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Products Grid */}
        <div className="w-full grid grid-cols-2 xs:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 lg:gap-5 mb-8">
          {products && products.length > 0 ? (
            products.map((product) => (
              <ProductCard key={product.id} data={product} />
            ))
          ) : (
            <div className="col-span-full text-center py-20">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                No products found
              </h3>
              <p className="text-gray-600 mb-6">
                There are no products in this category yet.
              </p>
              <button
                onClick={() => router.push("/shop")}
                className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors"
              >
                Browse All Products
              </button>
            </div>
          )}
        </div>

        {/* Pagination */}
        {products.length > 0 && totalPages > 1 && (
          <>
            <hr className="border-t-black/10 mb-6" />
            <Pagination className="flex flex-col sm:flex-row justify-between items-center gap-4">
              {/* Previous Button */}
              {currentPage > 1 ? (
                <PaginationPrevious
                  href={generatePageURL(Math.max(currentPage - 1, 1))}
                  className="border border-primary text-primary px-3 py-1 rounded"
                  onClick={(e) => {
                    e.preventDefault();
                    const newPage = Math.max(currentPage - 1, 1);
                    setCurrentPage(newPage);
                    updateURL({ page: newPage });
                    window.scrollTo({ top: 0, behavior: "smooth" });
                  }}
                >
                  Previous
                </PaginationPrevious>
              ) : (
                <span className="invisible border text-primary border-primary px-3 py-1 rounded">
                  Previous
                </span>
              )}

              {/* Page Numbers */}
              <PaginationContent className="flex space-x-1 sm:space-x-2 mx-auto overflow-x-auto max-w-full">
                {(() => {
                  const items = [];

                  const addPageNumber = (pageNum) => {
                    items.push(
                      <PaginationItem key={`page-${pageNum}`}>
                        <PaginationLink
                          href={generatePageURL(pageNum)}
                          className={`px-2 sm:px-3 py-1 rounded text-sm sm:text-base ${
                            currentPage === pageNum
                              ? "bg-primary text-white"
                              : "text-black/50 hover:text-black"
                          }`}
                          onClick={(e) => {
                            e.preventDefault();
                            setCurrentPage(pageNum);
                            updateURL({ page: pageNum });
                            window.scrollTo({ top: 0, behavior: "smooth" });
                          }}
                        >
                          {pageNum}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  };

                  const addEllipsis = (key) => {
                    items.push(
                      <PaginationItem key={`ellipsis-${key}`}>
                        <PaginationEllipsis className="text-black/50" />
                      </PaginationItem>
                    );
                  };

                  // Always add first page
                  addPageNumber(1);

                  // Logic for showing pages and ellipsis
                  if (currentPage > 3) {
                    addEllipsis("start");
                  }

                  // Pages around current page
                  for (
                    let i = Math.max(2, currentPage - 1);
                    i <= Math.min(totalPages - 1, currentPage + 1);
                    i++
                  ) {
                    addPageNumber(i);
                  }

                  // Add ellipsis if needed before last page
                  if (currentPage < totalPages - 2) {
                    addEllipsis("end");
                  }

                  // Always add last page if not the first page
                  if (totalPages > 1) {
                    addPageNumber(totalPages);
                  }

                  return items;
                })()}
              </PaginationContent>

              {/* Next Button */}
              {currentPage < totalPages ? (
                <PaginationNext
                  href={generatePageURL(currentPage + 1)}
                  className="border border-primary text-primary px-3 py-1 rounded"
                  onClick={(e) => {
                    e.preventDefault();
                    const newPage = currentPage + 1;
                    setCurrentPage(newPage);
                    updateURL({ page: newPage });
                    window.scrollTo({ top: 0, behavior: "smooth" });
                  }}
                >
                  Next
                </PaginationNext>
              ) : (
                <span className="invisible border text-primary border-primary px-3 py-1 rounded">
                  Next
                </span>
              )}
            </Pagination>
          </>
        )}
      </div>
    </main>
  );
}
