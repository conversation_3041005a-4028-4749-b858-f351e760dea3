import { MetadataRoute } from 'next'
import apiServiceWrapper from '@/lib/services/apiService'
import apiRoutes from '@/lib/constant'
import { generateStaticRoutes, createSitemapEntry, batchProcessSitemapEntries } from '@/lib/utils/sitemap'

// Function to fetch all products for sitemap
async function fetchAllProducts() {
  try {
    // Fetch products with a large per_page to get all products
    // You might need to implement pagination if you have many products
    const response = await apiServiceWrapper.get(`${apiRoutes.PRODUCTS}?per_page=1000`)

    if (!response.error && response.data) {
      return response.data
    }
    return []
  } catch (error) {
    console.error('Error fetching products for sitemap:', error)
    return []
  }
}

// Function to fetch all categories for sitemap
async function fetchAllCategories() {
  try {
    const response = await apiServiceWrapper.get(apiRoutes.CATEGORY)

    if (!response.error && response.data) {
      return response.data
    }
    return []
  } catch (error) {
    console.error('Error fetching categories for sitemap:', error)
    return []
  }
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://cromitopia.com'

  // Generate static routes using utility function
  const staticRoutes = generateStaticRoutes(baseUrl)

  // Fetch dynamic product routes
  const products = await fetchAllProducts()
  const productRoutes = batchProcessSitemapEntries(
    products,
    (product: any) => createSitemapEntry(
      `${baseUrl}/shop/product/${product.slug}`,
      product.updated_at,
      'weekly',
      0.8
    )
  )

  // Fetch dynamic category routes
  const categories = await fetchAllCategories()
  const categoryRoutes = batchProcessSitemapEntries(
    categories,
    (category: any) => createSitemapEntry(
      `${baseUrl}/shop?categories=${category.slug}`,
      category.updated_at,
      'weekly',
      0.7
    )
  )

  return [
    ...staticRoutes,
    ...productRoutes,
    ...categoryRoutes,
  ]
}
