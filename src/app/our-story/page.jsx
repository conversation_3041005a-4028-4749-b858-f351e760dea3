"use client";

import { useState, useEffect } from "react";
import { <PERSON>, Sparkles, Gift, Crown } from "lucide-react";
import { useRouter } from "next/navigation";

export default function OurStory() {
  const [isVisible, setIsVisible] = useState(false);
  const router = useRouter();

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const storyPoints = [
    {
      icon: <Heart className="w-6 h-6 text-white" />,
      title: "Born from Love",
      description:
        "CromiTopia was born from our endless love for all things kawaii and cute!",
    },
    {
      icon: <Crown className="w-6 h-6 text-white" />,
      title: "Quality First",
      description:
        "We handpick every adorable item to ensure maximum cuteness and quality.",
    },
    {
      icon: <Sparkles className="w-6 h-6 text-white" />,
      title: "Spreading Joy",
      description:
        "Our mission is to bring smiles and kawaii magic to your everyday life.",
    },
    {
      icon: <Gift className="w-6 h-6 text-white" />,
      title: "Perfect Gifts",
      description:
        "From plushies to accessories, we have the perfect kawaii gift for everyone!",
    },
  ];

  const FadeIn = ({ children, delay = 0 }) => (
    <div
      className={`transition-all duration-700 ease-out ${
        isVisible ? "translate-y-0 opacity-100" : "translate-y-8 opacity-0"
      }`}
      style={{ transitionDelay: `${delay}ms` }}
    >
      {children}
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <head>
        <title>Our Story | CromiTopia</title>
        <meta name="description" content="Our story of kawaii and cute" />
      </head>
      {/* Simple background decoration */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-20 w-32 h-32 bg-[#407b82] rounded-full opacity-5"></div>
        <div className="absolute bottom-32 right-32 w-24 h-24 bg-[#407b82] rounded-full opacity-8"></div>
        <div className="absolute top-1/2 left-1/3 w-16 h-16 bg-[#5a9aa0] rounded-full opacity-6"></div>
      </div>

      <div className="relative z-10 container mx-auto px-6 py-16 max-w-6xl">
        {/* Clean Header */}
        <FadeIn delay={0}>
          <div className="text-center mb-20">
            <h1 className="text-5xl md:text-7xl font-bold text-[#407b82] mb-6">
              CromiTopia
            </h1>
            <div className="flex items-center justify-center gap-3 text-xl text-gray-600 mb-6">
              <Heart className="w-5 h-5 text-[#407b82]" />
              <span className="font-medium">Our Kawaii Story</span>
              <Heart className="w-5 h-5 text-[#407b82]" />
            </div>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Welcome to our magical world of cuteness! Discover how CromiTopia
              became your favorite kawaii wonderland.
            </p>
          </div>
        </FadeIn>

        {/* Main Story Section */}
        <FadeIn delay={200}>
          <div className="bg-white rounded-2xl shadow-lg p-8 md:p-12 mb-16 border border-gray-100">
            <div className="grid md:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl font-bold text-[#407b82] mb-6 flex items-center gap-3">
                  <Sparkles className="w-7 h-7" />
                  Once Upon a Time...
                </h2>
                <div className="space-y-4 text-gray-700 text-base leading-relaxed">
                  <p>
                    In a world that sometimes feels too serious, we dreamed of
                    creating a place where cuteness reigns supreme and every day
                    feels like a kawaii adventure! 🌈
                  </p>
                  <p>
                    CromiTopia was born from our obsession with all things
                    adorable - from squishy plushies that make you go "awww" to
                    sparkly accessories that add magic to your everyday life.
                  </p>
                  <p>
                    We believe that kawaii isn't just a style, it's a lifestyle
                    that brings joy, positivity, and endless smiles to everyone
                    who embraces it!
                  </p>
                </div>
              </div>
              <div className="flex justify-center">
                <div className="bg-gradient-to-br from-[#407b82] to-[#5a9aa0] rounded-2xl p-8 text-white text-center shadow-lg transform hover:scale-105 transition-transform duration-300">
                  <div className="text-4xl mb-4">🌟</div>
                  <h3 className="text-2xl font-bold mb-2">2020</h3>
                  <p className="opacity-90">The year CromiTopia was born!</p>
                </div>
              </div>
            </div>
          </div>
        </FadeIn>

        {/* Story Points Grid */}
        <FadeIn delay={400}>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {storyPoints.map((point, index) => (
              <div
                key={index}
                className="bg-white rounded-xl p-6 shadow-md border border-gray-100 hover:shadow-lg hover:-translate-y-1 transition-all duration-300"
              >
                <div className="text-center">
                  <div className="w-12 h-12 bg-[#407b82] rounded-full flex items-center justify-center mx-auto mb-4">
                    {point.icon}
                  </div>
                  <h3 className="text-lg font-bold text-gray-800 mb-3">
                    {point.title}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {point.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </FadeIn>

        {/* Simple CTA */}
        <FadeIn delay={800}>
          <div className="text-center bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">
              Join Our Kawaii Journey!
            </h2>
            <p className="text-gray-600 mb-6">
              Want to be part of the CromiTopia family? We'd love to hear from
              you!
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <button
                onClick={() => router.push("/contact-us")}
                className="bg-[#407b82] hover:bg-[#356268] text-white px-8 py-3 rounded-full font-medium transition-colors duration-200 shadow-md hover:shadow-lg"
              >
                Contact Us 💌
              </button>
              <button
                onClick={() => router.push("/shop")}
                className="bg-[#5a9aa0] hover:bg-[#4a8289] text-white px-8 py-3 rounded-full font-medium transition-colors duration-200 shadow-md hover:shadow-lg"
              >
                Shop Now 🛍️
              </button>
            </div>
          </div>
        </FadeIn>
      </div>
    </div>
  );
}
