"use client";
import { useState, useEffect } from "react";
import Head from "next/head";
import Image from "next/image";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import Link from "next/link";
import { Eye, EyeOff } from "lucide-react";

export default function Home() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <div className="flex min-h-screen bg-black justify-center items-center p-4">
      <Head>
        <title>Login Page</title>
        <meta name="description" content="Login page with floral design" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="relative bg-white rounded-lg overflow-hidden shadow-xl max-w-5xl w-full">
        {isMobile ? <MobileView /> : <DesktopView />}
      </div>
    </div>
  );
}

function DesktopView() {
  return (
    <div className="flex">
      <div className="w-1/2 p-8 flex flex-col justify-center">
        <div className="max-w-md mx-auto">
          <h2 className="text-2xl font-bold mb-1">Welcome Back 👋</h2>
          <p className="text-gray-600 mb-8">
            Today is a new day. It's your day. You shape it.
            <br />
            Sign in to dive into Kawaii World.
          </p>

          <LoginForm />

          <div className="mt-6 text-center">
            <p className="text-gray-600">
              Don't you have an account?{" "}
              <Link href="/register" className="text-blue-600 hover:underline">
                Sign up
              </Link>
            </p>
          </div>

          <div className="mt-12 text-center text-xs text-gray-500">
            © {new Date().getFullYear()} All rights reserved CromiTopia
          </div>
        </div>
      </div>

      <div className="w-1/2 relative">
        <div className="absolute inset-0">
          <Image
            src="/floral-image.jpeg"
            alt="Floral arrangement"
            layout="fill"
            objectFit="cover"
            className="h-full w-full"
          />
        </div>
      </div>
    </div>
  );
}

function MobileView() {
  return (
    <div className="p-6">
      <div className="mb-4">
        <Image
          src="/floral-image.jpeg"
          alt="Floral arrangement"
          width={300}
          height={200}
          className="rounded-md mx-auto h-[120px]"
        />
      </div>

      <h2 className="text-xl font-bold mb-1">Welcome Back 👋</h2>
      <p className="text-sm text-gray-600 mb-4">
        Today is a new day. It's your day. You shape it.
        <br />
        Sign in to dive into Kawaii World.
      </p>

      <LoginForm isMobile={true} />

      {/* <div className="mt-4 text-center">
        <p className="text-sm text-gray-600">Or sign in with</p>
      </div> */}

      <div className="mt-4 text-center">
        <p className="text-sm text-gray-600">
          Don't you have an account?{" "}
          <Link href="/register" className="text-primary hover:underline">
            Sign up
          </Link>
        </p>
      </div>

      <div className="mt-6 text-center text-xs text-gray-500">
        © {new Date().getFullYear()} All rights reserved CromiTopia
      </div>
    </div>
  );
}

function LoginForm({ isMobile = false }) {
  const [formData, setFormData] = useState({ email: "", password: "" });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleInputChange = (e) => {
    const { id, value } = e.target;
    setFormData((prev) => ({ ...prev, [id]: value }));
  };

  const togglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.email || !formData.password) {
      toast.dismiss();
      toast.error("Please fill out all fields.");
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/login`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();
      if (result.success) {
        localStorage.setItem("token", result.data.token);
        toast.dismiss();
        toast.success("Login successful!");
        router.push("/");
      } else {
        toast.dismiss();
        toast.error(result.message || "Login failed");
      }
    } catch (error) {
      toast.dismiss();
      toast.error("An error occurred during login.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="mb-4">
        <label
          htmlFor="email"
          className="block text-gray-700 text-sm font-medium mb-1"
        >
          Email
        </label>
        <input
          type="email"
          id="email"
          value={formData.email}
          onChange={handleInputChange}
          placeholder="<EMAIL>"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
        />
      </div>
      <div className="mb-2">
        <label
          htmlFor="password"
          className="block text-gray-700 text-sm font-medium mb-1"
        >
          Password
        </label>
        <div className="relative">
          <input
            type={showPassword ? "text" : "password"}
            id="password"
            value={formData.password}
            onChange={handleInputChange}
            placeholder="At least 8 characters"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
          <button
            type="button"
            onClick={togglePasswordVisibility}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            {showPassword ? (
              <Eye className="w-5 h-5" />
            ) : (
              <EyeOff className="w-5 h-5" />
            )}
          </button>
        </div>
      </div>
      {/* <div className="flex justify-end mb-6">
        <a href="#" className="text-sm text-blue-600 hover:underline">
          Forgot Password?
        </a>
      </div> */}
      <button
        type="submit"
        disabled={loading}
        className={`w-full py-2 px-4 rounded-md transition duration-300 ${
          loading
            ? "bg-primary cursor-not-allowed"
            : "bg-primary hover:bg-primary/80 text-white"
        }`}
      >
        {loading ? "Signing In..." : "Sign In"}
      </button>
    </form>
  );
}
