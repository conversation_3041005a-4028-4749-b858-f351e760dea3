"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import {
  Search,
  Package,
  Truck,
  CheckCircle,
  AlertCircle,
  Mail,
  Phone,
} from "lucide-react";
import toast from "react-hot-toast";
import { Toaster } from "react-hot-toast";

export default function OrderTracking() {
  const [formData, setFormData] = useState({
    orderId: "",
    email: "",
    phone: "",
  });
  const [contactMethod, setContactMethod] = useState("email"); // "email" or "phone"
  const [trackingData, setTrackingData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const router = useRouter();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    // Dismiss any existing toasts
    toast.dismiss();

    try {
      // Validate inputs
      if (!formData.orderId.trim()) {
        throw new Error("Order ID is required");
      }

      if (contactMethod === "email" && !formData.email.trim()) {
        throw new Error("Email is required");
      }

      if (contactMethod === "phone" && !formData.phone.trim()) {
        throw new Error("Mobile number is required");
      }

      // Clean order ID (remove # if present)
      const cleanOrderId = formData.orderId.replace("#", "");

      // Prepare request body based on contact method
      const requestBody = {
        code: cleanOrderId,
      };

      if (contactMethod === "email") {
        requestBody.email = formData.email;
      } else {
        requestBody.phone = formData.phone;
      }

      // Make API request
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/order-tracking`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        }
      );

      if (!response.ok) {
        // Handle HTTP errors
        const errorText = await response.text();
        try {
          const errorJson = JSON.parse(errorText);
          throw new Error(errorJson.message || `${response.status}`);
        } catch (e) {
          throw new Error(e);
        }
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || "Failed to track order");
      }

      setTrackingData(data.data);
      toast.success("Order found! Showing tracking details.", {
        duration: 3000,
        icon: "🎉",
      });
    } catch (response) {
      setTrackingData(null);
      setError(response.message);
      toast.error(response.message, {
        duration: 4000,
      });
    } finally {
      setLoading(false);
    }
  };

  // Helper function to get status icon and color
  const getStatusDetails = (status) => {
    const statusMap = {
      // Original statuses
      pending: {
        icon: <AlertCircle className="w-6 h-6" />,
        color: "text-yellow-500 bg-yellow-100",
        label: "Pending",
      },
      processing: {
        icon: <Package className="w-6 h-6" />,
        color: "text-blue-500 bg-blue-100",
        label: "Processing",
      },
      shipping: {
        icon: <Truck className="w-6 h-6" />,
        color: "text-purple-500 bg-purple-100",
        label: "Shipping",
      },
      delivered: {
        icon: <CheckCircle className="w-6 h-6" />,
        color: "text-green-500 bg-green-100",
        label: "Delivered",
      },
      completed: {
        icon: <CheckCircle className="w-6 h-6" />,
        color: "text-green-500 bg-green-100",
        label: "Completed",
      },
      canceled: {
        icon: <AlertCircle className="w-6 h-6" />,
        color: "text-red-500 bg-red-100",
        label: "Canceled",
      },

      // New enum values from OrderHistoryActionEnum
      cancel_order: {
        icon: <AlertCircle className="w-6 h-6" />,
        color: "text-red-500 bg-red-100",
        label: "Order Canceled",
      },
      cancel_shipment: {
        icon: <AlertCircle className="w-6 h-6" />,
        color: "text-red-500 bg-red-100",
        label: "Shipment Canceled",
      },
      confirm_order: {
        icon: <CheckCircle className="w-6 h-6" />,
        color: "text-blue-500 bg-blue-100",
        label: "Order Confirmed",
      },
      confirm_payment: {
        icon: <CheckCircle className="w-6 h-6" />,
        color: "text-green-500 bg-green-100",
        label: "Payment Confirmed",
      },
      create_order: {
        icon: <Package className="w-6 h-6" />,
        color: "text-blue-500 bg-blue-100",
        label: "Order Created",
      },
      create_order_from_admin_page: {
        icon: <Package className="w-6 h-6" />,
        color: "text-blue-500 bg-blue-100",
        label: "Order Created by Admin",
      },
      create_order_from_payment_page: {
        icon: <Package className="w-6 h-6" />,
        color: "text-blue-500 bg-blue-100",
        label: "Order Created from Payment",
      },
      create_order_from_seeder: {
        icon: <Package className="w-6 h-6" />,
        color: "text-gray-500 bg-gray-100",
        label: "Order Created from Seeder",
      },
      create_shipment: {
        icon: <Truck className="w-6 h-6" />,
        color: "text-purple-500 bg-purple-100",
        label: "Shipment Created",
      },
      mark_order_as_completed: {
        icon: <CheckCircle className="w-6 h-6" />,
        color: "text-green-500 bg-green-100",
        label: "Order Completed",
      },
      refund: {
        icon: <AlertCircle className="w-6 h-6" />,
        color: "text-orange-500 bg-orange-100",
        label: "Order Refunded",
      },
      return_order: {
        icon: <AlertCircle className="w-6 h-6" />,
        color: "text-orange-500 bg-orange-100",
        label: "Order Returned",
      },
      send_order_confirmation_email: {
        icon: <CheckCircle className="w-6 h-6" />,
        color: "text-blue-500 bg-blue-100",
        label: "Confirmation Email Sent",
      },
      update_cod_status: {
        icon: <Package className="w-6 h-6" />,
        color: "text-blue-500 bg-blue-100",
        label: "COD Status Updated",
      },
      update_shipping_status: {
        icon: <Truck className="w-6 h-6" />,
        color: "text-purple-500 bg-purple-100",
        label: "Shipping Status Updated",
      },
      update_status: {
        icon: <Package className="w-6 h-6" />,
        color: "text-blue-500 bg-blue-100",
        label: "Status Updated",
      },
      confirm_delivery: {
        icon: <CheckCircle className="w-6 h-6" />,
        color: "text-green-500 bg-green-100",
        label: "Delivery Confirmed",
      },
    };

    return (
      statusMap[status] || {
        icon: <Package className="w-6 h-6" />,
        color: "text-gray-500 bg-gray-100",
        label: status
          .replace(/_/g, " ")
          .replace(/\b\w/g, (l) => l.toUpperCase()),
      }
    );
  };

  return (
    <div className="min-h-screen bg-primary/5 py-12 px-4">
      <Toaster position="top-center" reverseOrder={false} gutter={8} />
      <div className="max-w-3xl mx-auto">
        {/* Kawaii Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-primary mb-2">
            Track Your Order
          </h1>
          <p className="text-gray-600 max-w-md mx-auto">
            Enter your order ID and contact information to see where your cute
            items are!
          </p>
        </div>

        {/* Tracking Form */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-8 border-2 border-primary/20">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label
                htmlFor="orderId"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Order ID
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Package className="h-5 w-5 text-primary/70" />
                </div>
                <input
                  type="text"
                  id="orderId"
                  name="orderId"
                  value={formData.orderId}
                  onChange={handleChange}
                  placeholder="e.g. #***********"
                  className="pl-10 w-full py-3 border-2 border-primary/20 rounded-xl focus:ring-primary focus:border-primary"
                  required
                />
              </div>
            </div>

            {/* Contact Method Toggle */}
            <div className="flex space-x-4 mb-2">
              <button
                type="button"
                onClick={() => setContactMethod("email")}
                className={`flex-1 py-2 px-4 rounded-lg flex items-center justify-center ${
                  contactMethod === "email"
                    ? "bg-primary/10 text-primary border-2 border-primary/30"
                    : "bg-gray-100 text-gray-700 border-2 border-gray-200"
                }`}
              >
                <Mail className="w-4 h-4 mr-2" />
                Email
              </button>
              <button
                type="button"
                onClick={() => setContactMethod("phone")}
                className={`flex-1 py-2 px-4 rounded-lg flex items-center justify-center ${
                  contactMethod === "phone"
                    ? "bg-primary/10 text-primary border-2 border-primary/30"
                    : "bg-gray-100 text-gray-700 border-2 border-gray-200"
                }`}
              >
                <Phone className="w-4 h-4 mr-2" />
                Mobile
              </button>
            </div>

            {/* Email Input (shown when email is selected) */}
            {contactMethod === "email" && (
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Email Address
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-5 w-5 text-primary/70" />
                  </div>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    placeholder="Enter your email address"
                    className="pl-10 w-full py-3 border-2 border-primary/20 rounded-xl focus:ring-primary focus:border-primary"
                    required={contactMethod === "email"}
                  />
                </div>
              </div>
            )}

            {/* Phone Input (shown when phone is selected) */}
            {contactMethod === "phone" && (
              <div>
                <label
                  htmlFor="phone"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Mobile Number
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Phone className="h-5 w-5 text-primary/70" />
                  </div>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    placeholder="Enter your mobile number"
                    className="pl-10 w-full py-3 border-2 border-primary/20 rounded-xl focus:ring-primary focus:border-primary"
                    required={contactMethod === "phone"}
                  />
                </div>
              </div>
            )}

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-primary hover:bg-primary/90 text-primary-foreground font-medium py-3 px-4 rounded-xl transition duration-200 flex items-center justify-center"
            >
              {loading ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-2 h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Tracking...
                </>
              ) : (
                <>
                  <Search className="mr-2 h-5 w-5" />
                  Track Order
                </>
              )}
            </button>
          </form>
        </div>

        {/* Tracking Results */}
        {trackingData && (
          <div className="bg-white rounded-2xl shadow-lg p-6 border-2 border-primary/20 animate-fadeIn">
            {/* Order Summary */}
            <div className="mb-6 pb-6 border-b border-primary/10">
              <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                <Package className="mr-2 text-primary" />
                Order Summary
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-primary/5 p-4 rounded-xl">
                  <p className="text-sm text-gray-500">Order ID</p>
                  <p className="font-medium text-gray-800">
                    {trackingData.order_summary.order_id}
                  </p>
                </div>
                <div className="bg-primary/5 p-4 rounded-xl">
                  <p className="text-sm text-gray-500">Status</p>
                  <div className="flex items-center">
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${
                        getStatusDetails(trackingData.order_summary.status)
                          .color
                      }`}
                    >
                      {trackingData.order_summary.status.replace(/_/g, " ")}
                    </span>
                  </div>
                </div>
                <div className="bg-primary/5 p-4 rounded-xl">
                  <p className="text-sm text-gray-500">Order Date</p>
                  <p className="font-medium text-gray-800">
                    {new Date(
                      trackingData.order_summary.order_date
                    ).toLocaleDateString("en-US", {
                      year: "numeric",
                      month: "short",
                      day: "numeric",
                    })}
                  </p>
                </div>
              </div>
            </div>

            {/* Shipment Timeline */}
            <div>
              <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                <Truck className="mr-2 text-primary" />
                Shipment Progress
              </h2>

              <div className="relative">
                {/* Timeline line */}
                <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-primary/20"></div>

                {/* Progress steps */}
                <div className="space-y-6">
                  {trackingData.shipment_details.progress.map((step, index) => {
                    const { icon, color, label } = getStatusDetails(
                      step.status
                    );
                    return (
                      <div key={index} className="relative flex items-start">
                        <div
                          className={`flex items-center justify-center w-12 h-12 rounded-full ${color} z-10`}
                        >
                          {icon}
                        </div>
                        <div className="ml-4 pt-1">
                          <h3 className="text-lg font-medium text-gray-900">
                            {label}
                          </h3>
                          <time className="text-sm text-primary font-medium">
                            {step.date}
                          </time>
                          {/* <p className="mt-1 text-sm text-gray-600">
                            {step.description}
                          </p> */}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Kawaii Footer */}
            <div className="mt-8 text-center">
              <p className="text-primary font-medium mt-2">
                Thank you for shopping with CromiTopia! ♡
              </p>
            </div>
          </div>
        )}

        {/* No results state - improved version */}
        {error && !loading && !trackingData && (
          <div className="bg-white rounded-2xl shadow-lg p-6 text-center border-2 border-primary/20 animate-fadeIn">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Oops! We couldn't find your order
            </h3>
            <p className="text-gray-600 mb-4">
              {error === "Order ID is required" ||
              error === "Email is required" ||
              error === "Mobile number is required"
                ? "Please fill in all required fields."
                : error.includes("not found")
                  ? "We couldn't find an order matching these details. Please check and try again."
                  : "There was a problem tracking your order. Please check your details and try again."}
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <button
                onClick={() => setError(null)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                Try Again
              </button>
              <button
                onClick={() => router.push("/contact-us")}
                className="inline-flex items-center px-4 py-2 border border-primary/30 text-sm font-medium rounded-md shadow-sm text-primary bg-white hover:bg-primary/10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                Contact Support
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
