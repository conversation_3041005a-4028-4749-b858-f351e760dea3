import { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://cromitopia.com'
  
  return {
    rules: {
      userAgent: '*',
      allow: '/',
      disallow: [
        '/my-profile',
        '/orders',
        '/order-tracking',
        '/checkout',
        '/addresses',
        '/wishlist',
        '/api/',
      ],
    },
    sitemap: `${baseUrl}/sitemap.xml`,
  }
}
