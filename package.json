{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write . && eslint . --fix"}, "dependencies": {"@next/third-parties": "^15.2.4", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@reduxjs/toolkit": "^2.2.7", "@types/crypto-js": "^4.2.2", "axios": "^1.8.4", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "embla-carousel-react": "^8.2.1", "framer-motion": "^12.19.2", "js-cookie": "^3.0.5", "lucide-react": "^0.438.0", "next": "14.2.7", "react": "^18", "react-dom": "^18", "react-hot-toast": "^2.5.2", "react-icons": "^5.3.0", "react-razorpay": "^3.0.1", "react-redux": "^9.1.2", "react-simple-star-rating": "^5.1.7", "redux-persist": "^6.0.0", "tailwind-merge": "^2.5.2", "tailwind-scrollbar": "^4.0.2", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.0", "vaul": "^1.1.2"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.4.0", "postcss": "^8", "prettier": "^3.5.3", "tailwindcss": "^3.4.1", "typescript": "^5"}}