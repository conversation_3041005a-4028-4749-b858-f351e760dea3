{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/app/addresses/AddressModal.jsx", "src/app/addresses/ConfirmationModal.jsx", "src/app/addresses/page.jsx", "src/app/cart/page.jsx", "src/app/checkout/page.jsx", "src/app/contact-us/page.jsx", "src/app/login/page.jsx", "src/app/my-profile/AddAddressModal.jsx", "src/app/my-profile/ChangePassword.jsx", "src/app/my-profile/page.jsx", "src/app/orders/page.jsx", "src/app/policies/page.jsx", "src/app/register/page.jsx", "src/app/reviews/page.jsx", "src/app/shop/product/[...slug]/page.jsx", "src/app/shop/product/[...slug]/PageClient.jsx", "src/app/shop/page.jsx", "src/app/wishlist/page.jsx", "src/app/ClientLayout.jsx", "src/app/page.jsx", "src/app/layout.jsx", "src/app/providers.jsx", "src/components/carousel/index.jsx", "src/components/cart-page/BreadcrumbCart.jsx", "src/components/cart-page/ProductCard.jsx", "src/components/common/NewArrival.jsx", "src/components/common/ProductCard.jsx", "src/components/common/ReviewCard.jsx", "src/components/common/SubscriberModal.jsx", "src/components/common/TopSellingSec.jsx", "src/components/layout/Footer/index.jsx", "src/components/layout/Footer/LayoutSpacing.jsx", "src/components/layout/Footer/LinksSection.jsx", "src/components/layout/Footer/NewsLetterSection.jsx", "src/components/layout/Navbar/TopNavbar/CartBtn.jsx", "src/components/layout/Navbar/TopNavbar/DesktopNavbar.jsx", "src/components/layout/Navbar/TopNavbar/index.jsx", "src/components/layout/Navbar/TopNavbar/MenuItem.jsx", "src/components/layout/Navbar/TopNavbar/MenuList.jsx", "src/components/layout/Navbar/TopNavbar/MobileNavbar.jsx", "src/components/layout/Navbar/TopNavbar/ResTopNavbar.jsx", "src/components/layout/Navbar/TopNavbar/WishList.jsx", "src/components/product-page/Header/AddToCardSection.jsx", "src/components/product-page/Header/AddToCartBtn.jsx", "src/components/product-page/Header/ColorSelection.jsx", "src/components/product-page/Header/index.jsx", "src/components/product-page/Header/PhotoSection.jsx", "src/components/product-page/Header/SizeSelection.jsx", "src/components/product-page/Tabs/index.jsx", "src/components/product-page/Tabs/ProductDetails.jsx", "src/components/product-page/Tabs/ProductDetailsContent.jsx", "src/components/product-page/BreadcrumbProduct.jsx", "src/components/shop-page/filters/CategoriesSection.jsx", "src/components/shop-page/filters/ColorsSection.jsx", "src/components/shop-page/filters/DressStyleSection.jsx", "src/components/shop-page/filters/index.jsx", "src/components/shop-page/filters/MobileFilters.jsx", "src/components/shop-page/filters/PriceSection.jsx", "src/components/shop-page/filters/SizeSection.jsx", "src/components/shop-page/BreadcrumbShop.jsx", "src/components/storage/index.jsx", "src/components/ui/SpinnerbLoader/index.jsx", "src/components/ui/accordion.jsx", "src/components/ui/AnimatedCounter.jsx", "src/components/ui/breadcrumb.jsx", "src/components/ui/button.jsx", "src/components/ui/carousel.jsx", "src/components/ui/CartCounter.jsx", "src/components/ui/drawer.jsx", "src/components/ui/input-group.jsx", "src/components/ui/navigation-menu.jsx", "src/components/ui/pagination.jsx", "src/components/ui/select.jsx", "src/components/ui/Rating.jsx", "src/components/ui/separator.jsx", "src/components/ui/slider.jsx", "src/components/ui/sheet.jsx", "src/lib/store.js", "src/lib/utils.js", "src/lib/hooks/redux.jsx", "src/lib/hooks/useAuthenticated.jsx", "src/lib/hooks/useDebounce.jsx", "src/lib/features/addresses/addressSlice.js", "src/lib/features/carts/cartsSlice.js", "src/lib/features/products/productsSlice.js", "src/lib/features/profile/profileSlice.js", "src/lib/features/settings/settingSlice.js", "src/lib/features/wishlist/wishlistSlice.js", "src/lib/constant/index.js", "src/lib/context/SearchContext.jsx", "tailwind.config.js"], "exclude": ["node_modules"]}